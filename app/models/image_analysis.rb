# 图片分析模型
# == Schema Information
#
# Table name: image_analyses
#
#  id                                               :bigint           not null, primary key
#  ai_analysis                                      :text
#  analysis_type                                    :string
#  completed_at                                     :datetime
#  confidence_score                                 :decimal(, )
#  description                                      :text
#  image_url                                        :string
#  images(JSON field to store multiple images data) :text
#  metadata                                         :text
#  processing_time                                  :decimal(, )
#  recommendations                                  :text
#  status                                           :string
#  thumbnail_url                                    :string
#  created_at                                       :datetime         not null
#  updated_at                                       :datetime         not null
#  user_id                                          :bigint           not null
#
# Indexes
#
#  index_image_analyses_on_analysis_type              (analysis_type)
#  index_image_analyses_on_created_at                 (created_at)
#  index_image_analyses_on_status                     (status)
#  index_image_analyses_on_user_id                    (user_id)
#  index_image_analyses_on_user_id_and_analysis_type  (user_id,analysis_type)
#  index_image_analyses_on_user_id_and_created_at     (user_id,created_at)
#  index_image_analyses_on_user_id_and_status         (user_id,status)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class ImageAnalysis < ApplicationRecord
  belongs_to :user
  
  # 验证
  validates :image_url, presence: true, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }
  validates :analysis_type, presence: true, inclusion: { 
    in: %w[body_composition posture exercise_form injury_risk comprehensive],
    message: "%{value} is not a valid analysis type" 
  }
  validates :status, presence: true, inclusion: { 
    in: %w[pending processing completed failed],
    message: "%{value} is not a valid status" 
  }
  
  # 枚举
  enum :analysis_type, {
    body_composition: 'body_composition',
    posture: 'posture', 
    exercise_form: 'exercise_form',
    injury_risk: 'injury_risk',
    comprehensive: 'comprehensive'
  }, suffix: :analysis
  
  enum :status, {
    pending: 'pending',
    processing: 'processing',
    completed: 'completed',
    failed: 'failed'
  }, suffix: :status
  
  # JSON 字段序列化
  serialize :recommendations, type: Array, coder: JSON
  serialize :metadata, type: Hash, coder: JSON
  
  # 作用域
  scope :recent, -> { order(created_at: :desc) }
  scope :by_type, ->(type) { where(analysis_type: type) if type.present? }
  scope :by_status, ->(status) { where(status: status) if status.present? }
  scope :completed_successfully, -> { where(status: 'completed') }
  scope :in_progress, -> { where(status: ['pending', 'processing']) }
  
  # 实例方法
  def analysis_type_text
    case analysis_type
    when 'body_composition'
      'Body Composition Analysis'
    when 'posture'
      'Posture Analysis'
    when 'exercise_form'
      'Exercise Form Analysis'
    when 'injury_risk'
      'Injury Risk Assessment'
    when 'comprehensive'
      'Comprehensive Analysis'
    else
      analysis_type.humanize
    end
  end
  
  def status_text
    case status
    when 'pending'
      'Pending Analysis'
    when 'processing'
      'Processing...'
    when 'completed'
      'Analysis Complete'
    when 'failed'
      'Analysis Failed'
    else
      status.humanize
    end
  end
  
  def processing?
    status.in?(['pending', 'processing'])
  end
  
  def completed?
    status == 'completed'
  end
  
  def failed?
    status == 'failed'
  end
  
  def has_recommendations?
    recommendations.present? && recommendations.any?
  end
  
  def processing_duration
    return nil unless completed_at.present? && created_at.present?
    completed_at - created_at
  end
  
  def file_size_mb
    metadata&.dig('file_size_bytes')&.to_f&./(1.megabyte)&.round(2)
  end
  
  def image_dimensions
    return nil unless metadata.present?
    width = metadata['width']
    height = metadata['height']
    return nil unless width && height
    "#{width}x#{height}"
  end
  
  # 类方法
  def self.analysis_type_options
    analysis_types.keys.map do |key|
      { 
        value: key, 
        label: new.tap { |obj| obj.analysis_type = key }.analysis_type_text 
      }
    end
  end
  
  def self.status_options
    statuses.keys.map do |key|
      { 
        value: key, 
        label: new.tap { |obj| obj.status = key }.status_text 
      }
    end
  end
  
  # 统计方法
  def self.generate_statistics
    {
      total_analyses: count,
      completion_rate: completion_rate,
      average_processing_time: average_processing_time,
      popular_analysis_types: popular_analysis_types
    }
  end
  
  def self.completion_rate
    total = count
    return 0 if total.zero?
    (completed_successfully.count.to_f / total * 100).round(2)
  end
  
  def self.average_processing_time
    completed_successfully.where.not(processing_time: nil).average(:processing_time)&.round(2)
  end
  
  def self.popular_analysis_types
    group(:analysis_type).count.sort_by { |_, count| -count }.to_h
  end
  
  private
  
  # 回调方法
  before_save :set_completed_at, if: :status_changed_to_completed?
  before_save :ensure_recommendations_array
  before_save :ensure_metadata_hash
  
  def status_changed_to_completed?
    status_changed? && status == 'completed'
  end
  
  def set_completed_at
    self.completed_at = Time.current
  end
  
  def ensure_recommendations_array
    self.recommendations = [] if recommendations.nil?
  end
  
  def ensure_metadata_hash
    self.metadata = {} if metadata.nil?
  end
end
