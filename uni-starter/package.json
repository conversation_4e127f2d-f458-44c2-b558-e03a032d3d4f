{"id": "uni-starter", "displayName": "uni-starter", "version": "2.2.8", "description": "云端一体应用快速开发基本项目模版", "keywords": ["login", "登录", "搜索", "uni-id实例", "留言板"], "repository": "https://gitcode.net/dcloud/uni-starter", "engines": {"HBuilderX": "^3.2.6", "uni-app": "^4.07", "uni-app-x": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "unicloud-template-project", "darkmode": "x", "i18n": "√", "widescreen": "x"}, "uni_modules": {"dependencies": ["uni-id-pages"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": {"extVersion": "2.2.1", "minVersion": ""}}, "mp": {"weixin": "√", "alipay": "-", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}, "dependencies": {"qrcodejs2": "^0.0.2"}}