## 1.2.2（2022-01-19）
- 修复 在微信小程序中样式不生效的bug
## 1.2.1（2022-01-18）
- 新增 update 方法 ，在动态更新时间后，刷新组件
## 1.2.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-countdown](https://uniapp.dcloud.io/component/uniui/uni-countdown)
## 1.1.3（2021-10-18）
- 重构
- 新增 font-size 支持自定义字体大小
## 1.1.2（2021-08-24）
- 新增 支持国际化
## 1.1.1（2021-07-30）
- 优化 vue3下小程序事件警告的问题
## 1.1.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.5（2021-06-18）
- 修复 uni-countdown 重复赋值跳两秒的 bug
## 1.0.4（2021-05-12）
- 新增 组件示例地址
## 1.0.3（2021-05-08）
- 修复 uni-countdown 不能控制倒计时的 bug
## 1.0.2（2021-02-04）
- 调整为uni_modules目录规范
