## 1.1.19（2024-07-18）
- 修复 初始值传入 null 导致input报错的bug
## 1.1.18（2024-04-11）
- 修复 easyinput组件双向绑定问题
## 1.1.17（2024-03-28）
- 修复 在头条小程序下丢失事件绑定的问题
## 1.1.16（2024-03-20）
- 修复 在密码输入情况下 清除和小眼睛覆盖bug 在edge浏览器下显示双眼睛bug
## 1.1.15（2024-02-21）
- 新增 左侧插槽：left
## 1.1.14（2024-02-19）
- 修复 onBlur的emit传值错误
## 1.1.12（2024-01-29）
- 补充 adjust-position文档属性补充
## 1.1.11（2024-01-29）
- 补充 adjust-position属性传递值：（Boolean）当键盘弹起时，是否自动上推页面
## 1.1.10（2024-01-22）
- 去除 移除无用的log输出
## 1.1.9（2023-04-11）
- 修复 vue3 下 keyboardheightchange 事件报错的bug
## 1.1.8（2023-03-29）
- 优化 trim 属性默认值
## 1.1.7（2023-03-29）
- 新增 cursor-spacing 属性
## 1.1.6（2023-01-28）
- 新增 keyboardheightchange 事件，可监听键盘高度变化
## 1.1.5（2022-11-29）
- 优化 主题样式
## 1.1.4（2022-10-27）
- 修复 props 中背景颜色无默认值的bug
## 1.1.0（2022-06-30）

- 新增 在 uni-forms 1.4.0 中使用可以在 blur 时校验内容
- 新增 clear 事件，点击右侧叉号图标触发
- 新增 change 事件 ，仅在输入框失去焦点或用户按下回车时触发
- 优化 组件样式，组件获取焦点时高亮显示，图标颜色调整等

## 1.0.5（2022-06-07）

- 优化 clearable 显示策略

## 1.0.4（2022-06-07）

- 优化 clearable 显示策略

## 1.0.3（2022-05-20）

- 修复 关闭图标某些情况下无法取消的 bug

## 1.0.2（2022-04-12）

- 修复 默认值不生效的 bug

## 1.0.1（2022-04-02）

- 修复 value 不能为 0 的 bug

## 1.0.0（2021-11-19）

- 优化 组件 UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-easyinput](https://uniapp.dcloud.io/component/uniui/uni-easyinput)

## 0.1.4（2021-08-20）

- 修复 在 uni-forms 的动态表单中默认值校验不通过的 bug

## 0.1.3（2021-08-11）

- 修复 在 uni-forms 中重置表单，错误信息无法清除的问题

## 0.1.2（2021-07-30）

- 优化 vue3 下事件警告的问题

## 0.1.1

- 优化 errorMessage 属性支持 Boolean 类型

## 0.1.0（2021-07-13）

- 组件兼容 vue3，如何创建 vue3 项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)

## 0.0.16（2021-06-29）

- 修复 confirmType 属性（仅 type="text" 生效）导致多行文本框无法换行的 bug

## 0.0.15（2021-06-21）

- 修复 passwordIcon 属性拼写错误的 bug

## 0.0.14（2021-06-18）

- 新增 passwordIcon 属性，当 type=password 时是否显示小眼睛图标
- 修复 confirmType 属性不生效的问题

## 0.0.13（2021-06-04）

- 修复 disabled 状态可清出内容的 bug

## 0.0.12（2021-05-12）

- 新增 组件示例地址

## 0.0.11（2021-05-07）

- 修复 input-border 属性不生效的问题

## 0.0.10（2021-04-30）

- 修复 ios 遮挡文字、显示一半的问题

## 0.0.9（2021-02-05）

- 调整为 uni_modules 目录规范
- 优化 兼容 nvue 页面
