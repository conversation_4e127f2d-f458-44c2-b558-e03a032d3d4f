## 1.3.1（2021-12-20）
- 修复 在vue页面下略缩图显示不正常的bug
## 1.3.0（2021-11-19）
- 重构插槽的用法 ，header 替换为 title 
- 新增 actions 插槽
- 新增 cover 封面图属性和插槽
- 新增 padding 内容默认内边距离
- 新增 margin 卡片默认外边距离
- 新增 spacing 卡片默认内边距
- 新增 shadow 卡片阴影属性
- 取消 mode 属性，可使用组合插槽代替
- 取消 note 属性 ，使用actions插槽代替
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-card](https://uniapp.dcloud.io/component/uniui/uni-card)
## 1.2.1（2021-07-30）
- 优化 vue3下事件警告的问题
## 1.2.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.1.8（2021-07-01）
- 优化 图文卡片无图片加载时，提供占位图标
- 新增 header 插槽，自定义卡片头部（ 图文卡片 mode="style" 时，不支持）
- 修复 thumbnail 不存在仍然占位的 bug
## 1.1.7（2021-05-12）
- 新增 组件示例地址
## 1.1.6（2021-02-04）
- 调整为uni_modules目录规范
