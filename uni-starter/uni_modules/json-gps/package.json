{"id": "json-gps", "displayName": "地理位置获取方法，支持在onShow生命周期使用集成权限判断和引导开启（包括设备权限和应用权限）", "version": "1.0.0", "description": "支持在onShow生命周期使用集成权限判断和引导开启（包括设备权限和应用权限）的地理位置获取方法", "keywords": ["权限引导"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"category": ["JS SDK", "通用 SDK"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}