<template>
	<view class="container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<text class="nav-title">Sport Analysis</text>
		</view>
		
		<!-- 可滚动内容区域 -->
		<scroll-view class="scroll-content" scroll-y="true">
			<!-- 应用介绍 -->
			<view class="intro-section">
				<text class="intro-title">Motion</text>
				<text class="intro-description">
					Take photos or upload images to analyze your sports movements and get professional feedback on your form and technique.
				</text>
				<!-- 介绍图片 - 当没有上传图片时显示 -->
				<view class="intro-image-container" v-if="selectedImages.length === 0">
					<image src="/static/logo.png" class="intro-image" mode="aspectFit"></image>
				</view>
			</view>
			
			<!-- 图片展示区域 -->
			<view class="image-gallery" v-if="selectedImages.length > 0">
				<text class="gallery-title">Selected Images ({{selectedImages.length}}/9)</text>
				<view class="image-grid">
					<view 
						class="image-item" 
						:class="{ 'image-item-last': (index + 1) % 3 === 0 }"
						v-for="(image, index) in selectedImages" 
						:key="index"
						@click="previewImage(image, index)"
					>
						<image :src="image" class="image" mode="aspectFill"></image>
						<view class="delete-btn" @click.stop="deleteImage(index)">
							<text class="delete-text">×</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 操作按钮区域 -->
			<view class="action-section">
				<view class="button-row">
					<view class="action-button camera-btn" @click="takePhoto">
						<text class="button-icon">📷</text>
						<text class="button-text">Take Photo</text>
					</view>
					<view class="action-button upload-btn" @click="chooseImage">
						<text class="button-icon">📁</text>
						<text class="button-text">Upload Image</text>
					</view>
				</view>
				
				<!-- 只有选择了图片才显示清除和分析按钮 -->
				<view class="button-row bottom-buttons" v-if="selectedImages.length > 0">
					<view class="action-button clear-btn" @click="clearImages">
						<text class="button-text">Clear All</text>
					</view>
					<view class="action-button analyze-btn" @click="analyzeImages">
						<text class="button-text">Analyze</text>
					</view>
				</view>
				
				<!-- 底部留白，确保内容可以完全滚动到视野内 -->
				<view class="bottom-spacer"></view>
			</view>
		</scroll-view>
		
		<!-- 分析中遮罩 -->
		<view class="loading-mask" v-if="isAnalyzing">
			<view class="loading-content">
				<text class="loading-text">Analyzing...</text>
				<text class="loading-desc">Processing your images, please wait...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'
	export default {
		data() {
			return {
				statusBarHeight: 0,
				selectedImages: [],
				isAnalyzing: false
			}
		},
		async onLoad() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
		},
		methods: {
			// 拍照
			takePhoto() {
				if (this.selectedImages.length >= 9) {
					uni.showToast({
						title: 'Maximum 9 images allowed',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: 9 - this.selectedImages.length,
					sourceType: ['camera'],
					success: (res) => {
						this.selectedImages = [...this.selectedImages, ...res.tempFilePaths];
					},
					fail: (err) => {
						console.error('Take photo failed:', err);
					}
				});
			},
			
			// 选择图片
			chooseImage() {
				if (this.selectedImages.length >= 9) {
					uni.showToast({
						title: 'Maximum 9 images allowed',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: 9 - this.selectedImages.length,
					sourceType: ['album'],
					success: (res) => {
						this.selectedImages = [...this.selectedImages, ...res.tempFilePaths];
					},
					fail: (err) => {
						console.error('Choose image failed:', err);
					}
				});
			},
			
			// 预览图片
			previewImage(currentImage, index) {
				uni.previewImage({
					urls: this.selectedImages,
					current: index
				});
			},
			
			// 删除图片
			deleteImage(index) {
				this.selectedImages.splice(index, 1);
			},
			
			// 清空所有图片
			clearImages() {
				uni.showModal({
					title: 'Confirm',
					content: 'Are you sure you want to clear all images?',
					success: (res) => {
						if (res.confirm) {
							this.selectedImages = [];
						}
					}
				});
			},
			
			// 分析图片
			  async analyzeImages() {
				  if (this.selectedImages.length === 0) {
					  uni.showToast({
						  title: 'Please select images first',
						  icon: 'none'
					  });
					  return;
				  }

				  this.isAnalyzing = true;

				  try {
					  const uploadPromises = this.selectedImages.map(imagePath => {
						  return api.imageAnalysis.upload({
							  filePath: imagePath,
							  analysis_type: 'sport_posture' // Or any other relevant type
						  });
					  });

					  await Promise.all(uploadPromises);

					  this.isAnalyzing = false;
					  this.selectedImages = [];

					  uni.showToast({
						  title: 'Analysis started for all images!',
						  icon: 'success'
					  });

					  // Navigate to the analysis history page
					  uni.switchTab({
						  url: '/pages/grid/grid'
					  });

				  } catch (error) {
					  this.isAnalyzing = false;
					  console.error('Analysis failed:', error);
					  uni.showToast({
						  title: 'An error occurred during analysis. Please try again.',
						  icon: 'none'
					  });
				  }
			  }
		}
	}
</script>

<style scoped>
	.container {
		flex: 1;
		background-color: #1a1a1a;
	}

	.status-bar {
		background-color: #1a1a1a;
	}

	.nav-bar {
		height: 88rpx;
		background-color: #1a1a1a;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding-left: 30rpx;
		padding-right: 30rpx;
		border-bottom-width: 1rpx;
		border-bottom-color: #333333;
	}
	
	.nav-title {
		font-size: 36rpx;
		color: #ffffff;
		font-weight: bold;
	}
	
	.scroll-content {
		flex: 1;
	}
	
	.intro-section {
		padding: 60rpx 40rpx;
		background-color: #2d2d2d;
		margin: 20rpx;
		border-radius: 24rpx;
		border-width: 1rpx;
		border-color: #404040;
	}

	.intro-title {
		font-size: 48rpx;
		color: #ffffff;
		font-weight: bold;
		text-align: center;
		margin-bottom: 30rpx;
		letter-spacing: 2rpx;
	}

	.intro-description {
		font-size: 28rpx;
		color: #b0b0b0;
		line-height: 40rpx;
		text-align: center;
		margin-bottom: 40rpx;
	}

	.intro-image-container {
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
	}

	.intro-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 24rpx;
	}
	
	.image-gallery {
		margin: 20rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
	}
	
	.gallery-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 30rpx;
	}
	
	.image-grid {
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: flex-start;
	}

	.image-item {
		width: 200rpx;
		height: 200rpx;
		margin-right: 16rpx;
		margin-bottom: 20rpx;
		position: relative;
		border-radius: 10rpx;
		overflow: hidden;
	}

	.image-item-last {
		margin-right: 0;
	}	.image {
		width: 200rpx;
		height: 200rpx;
	}
	
	.delete-btn {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(255, 0, 0, 0.8);
		border-radius: 20rpx;
		align-items: center;
		justify-content: center;
	}
	
	.delete-text {
		color: #ffffff;
		font-size: 24rpx;
		font-weight: bold;
	}
	
	.action-section {
		padding: 40rpx;
	}
	
	.button-row {
		flex-direction: row;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}
	
	.action-button {
		flex: 1;
		height: 100rpx;
		background-color: #667eea;
		border-radius: 50rpx;
		align-items: center;
		justify-content: center;
		margin: 0 10rpx;
		flex-direction: row;
	}
	
	.camera-btn {
		background-color: #667eea;
	}
	
	.upload-btn {
		background-color: #764ba2;
	}
	
	.clear-btn {
		background-color: #f5576c;
	}
	
	.analyze-btn {
		background-color: #4CAF50;
	}
	
	.bottom-spacer {
		height: 100rpx;
	}
	
	.button-icon {
		font-size: 32rpx;
		margin-right: 15rpx;
	}
	
	.button-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
	}
	
	.bottom-buttons {
		margin-top: 20rpx;
	}
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}
	
	.loading-content {
		background-color: #ffffff;
		padding: 60rpx;
		border-radius: 20rpx;
		align-items: center;
	}
	
	.loading-text {
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.loading-desc {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
	}
</style>
