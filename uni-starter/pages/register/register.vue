<template>
	<view class="register-container">
		<!-- Top Area -->
		<view class="register-header">
			<view class="logo-wrapper">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			</view>
			<text class="app-name">Motion</text>
			<text class="app-slogan">Register New Account</text>
		</view>
		
		<!-- Registration Form -->
		<view class="register-form">
			<!-- Email Input -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.email" 
					:placeholder="$t('common.emailPlaceholder')"
					type="email"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="email" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- Verification Code Input -->
			<view class="input-group verification-group">
				<uni-easyinput 
					v-model="formData.verificationCode" 
					:placeholder="$t('common.verifyCodePlaceholder')"
					type="number"
					trim="both"
					clearable
					:inputBorder="false"
					maxlength="6"
				>
					<template v-slot:left>
						<uni-icons type="checkmarkempty" size="22" color="#999"></uni-icons>
					</template>
					<template v-slot:right>
						<button 
							class="verification-btn" 
							:class="{ 'verification-btn-disabled': countdown > 0 || !canSendCode }"
							:disabled="countdown > 0 || !canSendCode"
							@click="sendVerificationCode"
						>
							{{ countdown > 0 ? `${countdown}s` : $t('common.getVerifyCode') }}
						</button>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- Password Input -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.password" 
					:placeholder="$t('register.passwordDigitsPlaceholder')"
					type="password"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="locked" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- Register Button -->
			<button 
				class="register-btn" 
				:class="{ 'register-btn-disabled': !canRegister }"
				:disabled="!canRegister"
				@click="handleRegister"
			>
				{{ $t('register.registerAndLogin') }}
			</button>
			
			<!-- Feature Links -->
			<view class="register-links">
				<text class="link-text" @click="goToLogin">
					Already have an account? Login now
				</text>
			</view>
		</view>
		
		<!-- Bottom Agreement -->
		<view class="register-footer">
			<text class="agreement-text">
				By registering, you agree to the
				<text class="agreement-link" @click="showAgreement">
					Terms of Service
				</text>
				and
				<text class="agreement-link" @click="showPrivacy">
					Privacy Policy
				</text>
			</text>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'
	import {
		store,
		mutations
	} from '@/common/store.js'

	export default {
		data() {
			return {
				formData: {
					email: '',
					verificationCode: '',
					password: ''
				},
				countdown: 0,
				timer: null,
				isLoading: false
			}
		},
		computed: {
			appConfig() {
				return getApp().globalData.config
			},
			canSendCode() {
				return this.formData.email.trim() && this.validateEmail(this.formData.email)
			},
			canRegister() {
				return this.formData.email.trim() && 
					   this.formData.verificationCode.trim() && 
					   this.formData.password.trim() && 
					   !this.isLoading
			}
		},
		methods: {
			/**
			 * Send verification code
			 */
			async sendVerificationCode() {
				if (!this.canSendCode || this.countdown > 0) return
				
				try {
					const response = await api.auth.sendRegisterCode(this.formData.email.trim())
					
					if (response.success) {
						api.showSuccess('Verification code sent to your email')
						this.startCountdown()
					}
				} catch (error) {
					console.error('Failed to send verification code:', error)
					// Error handling is done in api.js
				}
			},
			
			/**
			 * Start countdown
			 */
			startCountdown() {
				this.countdown = 60
				this.timer = setInterval(() => {
					this.countdown--
					if (this.countdown <= 0) {
						clearInterval(this.timer)
						this.timer = null
					}
				}, 1000)
			},
			
			/**
			 * Handle registration
			 */
			async handleRegister() {
				if (!this.canRegister) return
				
				// Email format validation
				if (!this.validateEmail(this.formData.email)) {
					api.showError('Please enter a valid email address')
					return
				}
				
				// Verification code validation
				if (this.formData.verificationCode.length !== 6) {
					api.showError('Please enter a 6-digit verification code')
					return
				}
				
				// Password validation
				if (!this.validatePassword(this.formData.password)) {
					api.showError('Password must be 6-20 characters long')
					return
				}
				
				this.isLoading = true
				
				try {
					// Call registration API
					const response = await api.auth.register({
            verification_code: this.formData.verificationCode.trim(),
            user: {
              email: this.formData.email.trim(),
              password: this.formData.password.trim()
            }
					})
					console.info( 'register', response)
					// Registration successful
					if (response.success && response.data.token) {
						// Save token and user info
						api.setToken(response.data.token)
						api.setUserInfo(response.data.user)
						
						// Update user info in store
						mutations.loginSuccess(response.data.user)
						
						// Show success message
						api.showSuccess(response.message || 'Registration successful')
						
						// Delayed redirect
						setTimeout(() => {
							// this.goBack()
              uni.navigateTo({url: '/pages/ucenter/ucenter'})
						}, 1000)
					}
				} catch (error) {
					console.error('Registration failed:', error)
					// Error handling is done in api.js
				} finally {
					this.isLoading = false
				}
			},
			
			/**
			 * Validate email format
			 */
			validateEmail(email) {
				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
				return emailRegex.test(email)
			},
			
			/**
			 * Validate password format
			 */
			validatePassword(password) {
				return password.length >= 6 && password.length <= 20
			},
			
			/**
			 * Go back to previous page
			 */
			goBack() {
				const pages = getCurrentPages()
				if (pages.length > 1) {
					uni.navigateBack()
				} else {
					// If accessing registration page directly, go to home
					uni.switchTab({
						url: '/pages/ucenter/ucenter'
					})
				}
			},
			
			/**
			 * Go to login page
			 */
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			},
			
			/**
			 * Show user agreement
			 */
			showAgreement() {
				uni.navigateTo({
					url: '/pages/legal/legal?type=agreement'
				})
			},
			
			/**
			 * Show privacy policy
			 */
			showPrivacy() {
				uni.navigateTo({
					url: '/pages/legal/legal?type=privacy'
				})
			}
		},
		beforeDestroy() {
			// Clear timer
			if (this.timer) {
				clearInterval(this.timer)
				this.timer = null
			}
		}
	}
</script>

<style lang="scss">
	.register-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 0 40rpx;
		box-sizing: border-box;
	}
	
	.register-header {
		text-align: center;
		margin-bottom: 80rpx;
		
		.logo-wrapper {
			margin-bottom: 40rpx;
			
			.logo {
				width: 120rpx;
				height: 120rpx;
				border-radius: 20rpx;
				box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
			}
		}
		
		.app-name {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			margin-bottom: 20rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.app-slogan {
			display: block;
			font-size: 32rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 300;
		}
	}
	
	.register-form {
		width: 100%;
		max-width: 640rpx;
		margin-bottom: 60rpx;
		
		.input-group {
			margin-bottom: 30rpx;
			background: rgba(255, 255, 255, 0.95);
			border-radius: 25rpx;
			padding: 5rpx 30rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			backdrop-filter: blur(10rpx);
			
			&.verification-group {
				padding-right: 5rpx;
			}
			
			:deep(.uni-easyinput) {
				
				.uni-easyinput__content {
					height: 90rpx;
					
					.uni-easyinput__content-input {
						font-size: 32rpx;
						color: #333;
					}
				}
			}
			
			.verification-btn {
				background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
				border: none;
				border-radius: 20rpx;
				padding: 15rpx 30rpx;
				font-size: 24rpx;
				color: #fff;
				margin: 10rpx;
				
				&.verification-btn-disabled {
					background: #ccc;
				}
			}
		}
		
		.register-btn {
			width: 100%;
			height: 90rpx;
			background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);
			border: none;
			border-radius: 25rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #fff;
			box-shadow: 0 8rpx 25rpx rgba(255, 107, 149, 0.4);
			margin-top: 40rpx;
			margin-bottom: 40rpx;
			
			&.register-btn-disabled {
				background: #ccc;
				box-shadow: none;
			}
		}
		
		.register-links {
			text-align: center;
			
			.link-text {
				color: rgba(255, 255, 255, 0.9);
				font-size: 28rpx;
				text-decoration: underline;
			}
		}
	}
	
	.register-footer {
		text-align: center;
		padding: 0 40rpx;
		
		.agreement-text {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.7);
			line-height: 1.5;
			
			.agreement-link {
				color: rgba(255, 255, 255, 0.9);
				text-decoration: underline;
			}
		}
	}
</style>