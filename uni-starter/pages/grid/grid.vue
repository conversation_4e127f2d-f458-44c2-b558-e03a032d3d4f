<template>
	<view class="page">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>

		<!-- 导航栏 -->
		<view class="nav-bar">
			<text class="nav-title">Analysis</text>
		</view>

		<!-- 月份显示和日历选择 -->
		<view class="month-header">
			<text class="month-text">{{ currentMonthText }}</text>
			<picker mode="date" :value="currentDateString" @change="onDateChange" class="date-picker">
				<view class="calendar-icon">
					<text class="icon-calendar">📅</text>
				</view>
			</picker>
		</view>

		<!-- 7天日期选择区域 -->
		<view class="week-selector">
			<scroll-view class="week-scroll" scroll-x="true" :scroll-left="scrollLeft" scroll-with-animation="true"
				show-scrollbar="false" @touchstart.stop="handleTouchStart" @touchmove.stop="handleTouchMove"
				@touchend.stop="handleTouchEnd">
				<view class="week-container">
					<view v-for="(day, index) in weekDays" :key="index" class="day-item"
						:class="{ 'day-active': isSameDay(day.date, selectedDate) }" @click="selectDate(day.date)">
						<text class="day-week">{{ day.weekDay }}</text>
						<text class="day-number">{{ day.day }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 主内容区域 -->
		<view class="main-content">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading">
				<text class="loading-text">Loading...</text>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!hasAnalysisData" class="empty">
				<text class="empty-icon">📊</text>
				<text class="empty-title">No Analysis Data</text>
				<text class="empty-desc">No workout analysis records for {{ formatDate(selectedDate) }}</text>
			</view>

			<!-- 分析数据列表 -->
			<scroll-view v-else class="analysis-scroll" scroll-y="true">
				<view v-for="item in analysisData" :key="item.id" class="analysis-item">
					<!-- 项目头部 -->
					<view class="item-header" @click="toggleExpand(item.id)">
						<view class="item-info">
							<text class="item-title">{{ item.type }}</text>
							<text class="item-time">{{ item.time }}</text>
						</view>
						<view class="item-score" v-if="item.status === 'completed'">
							<text class="score-num">{{ item.score }}</text>
							<text class="score-text">pts</text>
						</view>
						<!-- 分析状态显示 -->
						<view class="status-container">
							<view v-if="item.status === 'analyzing'" class="status analyzing">
								<view class="spinner"></view>
								<text>Analyzing</text>
							</view>
							<view v-else-if="item.status === 'pending'" class="status pending">
								<text>Pending</text>
							</view>
							<view v-else-if="item.status === 'completed'" class="status completed">
								<text>Completed</text>
							</view>
							<view v-else-if="item.status === 'failed'" class="status failed">
								<text>Failed</text>
							</view>
						</view>
						<!-- 只有completed状态的分析才能显示展开按钮 -->
						<text v-if="item.status === 'completed'"
							class="expand-icon">{{ expandedItems[item.id] ? '−' : '+' }}</text>
					</view>

					<!-- 分析相关图片 -->
					<view v-if="item.images && item.images.length > 0" class="report-images-section">
						<text class="images-section-title">Analysis Images</text>
						<view class="report-images-grid">
							<view v-for="(image, index) in item.images" :key="index" class="report-image-item"
								:class="{ 'report-image-item-last': (index + 1) % 3 === 0 }"
								@tap.stop="previewImage(image, item.images)">
								<image :src="image" class="report-image" mode="aspectFill"></image>
							</view>
						</view>
					</view>

					<!-- 详细信息（展开时显示） -->
					<view v-if="expandedItems[item.id]" class="detailed-info">
						<!-- 基础统计 -->
						<view class="basic-stats">
							<view class="stat-item">
								<text class="stat-value">{{ item.duration }}</text>
								<text class="stat-label">Duration</text>
							</view>
							<view class="stat-item" v-if="item.distance">
								<text class="stat-value">{{ item.distance }}</text>
								<text class="stat-label">Distance</text>
							</view>
							<view class="stat-item" v-if="item.sets">
								<text class="stat-value">{{ item.sets }}组</text>
								<text class="stat-label">Sets</text>
							</view>
							<view class="stat-item">
								<text class="stat-value">{{ item.calories }}</text>
								<text class="stat-label">Calories</text>
							</view>
							<view class="stat-item">
								<text class="stat-value">{{ item.avgPace || item.targetMuscle }}</text>
								<text class="stat-label">{{ item.avgPace ? 'Avg Pace' : 'Target Muscle' }}</text>
							</view>
						</view>

						<!-- 1. 整体表现 -->
						<view class="info-section">
							<text class="section-header">Overall Performance</text>
							<text class="section-content">{{ item.analysis.overallPerformance }}</text>
						</view>

						<!-- 2. 配速与心率分析（每公里）-->
						<view v-if="item.analysis.paceAnalysis" class="info-section">
							<text class="section-header">Pace & Heart Rate Analysis (Per KM)</text>
							<view class="pace-list">
								<view v-for="(pace, index) in item.analysis.paceAnalysis" :key="index" class="pace-row">
									<text class="pace-km">{{ pace.km }}km</text>
									<text class="pace-time">{{ pace.pace }}</text>
									<text class="pace-hr">{{ pace.heartRate }}</text>
								</view>
							</view>
						</view>

						<!-- 3. 心率区间分布 -->
						<view v-if="item.analysis.heartRateZones" class="info-section">
							<text class="section-header">Heart Rate Zone Distribution</text>
							<view class="hr-zones">
								<view v-for="(zone, index) in item.analysis.heartRateZones" :key="index"
									class="hr-zone">
									<text class="zone-name">{{ zone.zone }}</text>
									<text class="zone-time">{{ zone.time }}</text>
									<text class="zone-percent">{{ zone.percentage }}</text>
									<text class="zone-desc">{{ zone.description }}</text>
								</view>
							</view>
						</view>

						<!-- 4. 热量消耗与能量代谢分析 -->
						<view v-if="item.analysis.calorieMetabolism" class="info-section">
							<text class="section-header">Calorie & Metabolism Analysis</text>
							<view class="metabolism-grid">
								<view class="metabolism-item">
									<text class="metabolism-label">Total Burn</text>
									<text
										class="metabolism-value">{{ item.analysis.calorieMetabolism.totalCalories }}kcal</text>
								</view>
								<view class="metabolism-item">
									<text class="metabolism-label">Fat Burn</text>
									<text class="metabolism-value">{{ item.analysis.calorieMetabolism.fatBurn }}</text>
								</view>
								<view class="metabolism-item">
									<text class="metabolism-label">Carbs Burn</text>
									<text
										class="metabolism-value">{{ item.analysis.calorieMetabolism.carbohydrateBurn }}</text>
								</view>
								<view class="metabolism-item">
									<text class="metabolism-label">Metabolism</text>
									<text
										class="metabolism-value">{{ item.analysis.calorieMetabolism.metabolicRate }}</text>
								</view>
								<view class="metabolism-item">
									<text class="metabolism-label">Afterburn</text>
									<text
										class="metabolism-value">{{ item.analysis.calorieMetabolism.afterburnEffect }}</text>
								</view>
							</view>
						</view>

						<!-- 5. 步频与跑步效率（跑步项目）-->
						<view v-if="item.analysis.runningEfficiency" class="info-section">
							<text class="section-header">Cadence & Running Efficiency</text>
							<view class="efficiency-grid">
								<view class="efficiency-item">
									<text class="efficiency-label">Avg Cadence</text>
									<text
										class="efficiency-value">{{ item.analysis.runningEfficiency.avgCadence }}</text>
								</view>
								<view class="efficiency-item">
									<text class="efficiency-label">Stride Length</text>
									<text
										class="efficiency-value">{{ item.analysis.runningEfficiency.strideLength }}</text>
								</view>
								<view class="efficiency-item">
									<text class="efficiency-label">Efficiency</text>
									<text
										class="efficiency-value">{{ item.analysis.runningEfficiency.efficiency }}</text>
								</view>
								<view class="efficiency-item">
									<text class="efficiency-label">vs Last Week</text>
									<text
										class="efficiency-value">{{ item.analysis.runningEfficiency.improvement }}</text>
								</view>
							</view>
						</view>

						<!-- 5. 训练效率（力量训练项目）-->
						<view v-if="item.analysis.trainingEfficiency" class="info-section">
							<text class="section-header">Training Efficiency Analysis</text>
							<view class="efficiency-grid">
								<view class="efficiency-item">
									<text class="efficiency-label">Volume Load</text>
									<text
										class="efficiency-value">{{ item.analysis.trainingEfficiency.volumeLoad }}</text>
								</view>
								<view class="efficiency-item">
									<text class="efficiency-label">Intensity</text>
									<text
										class="efficiency-value">{{ item.analysis.trainingEfficiency.intensity }}</text>
								</view>
								<view class="efficiency-item">
									<text class="efficiency-label">Rest Time</text>
									<text
										class="efficiency-value">{{ item.analysis.trainingEfficiency.restTime }}</text>
								</view>
								<view class="efficiency-item">
									<text class="efficiency-label">Overall</text>
									<text
										class="efficiency-value">{{ item.analysis.trainingEfficiency.efficiency }}</text>
								</view>
							</view>
						</view>

						<!-- 6. 环境系数影响 -->
						<view v-if="item.analysis.environmentalFactors" class="info-section">
							<text class="section-header">Environmental Factors</text>
							<view class="environment-grid">
								<view class="environment-item">
									<text class="environment-label">Temperature</text>
									<text
										class="environment-value">{{ item.analysis.environmentalFactors.temperature }}</text>
								</view>
								<view class="environment-item">
									<text class="environment-label">Humidity</text>
									<text
										class="environment-value">{{ item.analysis.environmentalFactors.humidity }}</text>
								</view>
								<view class="environment-item" v-if="item.analysis.environmentalFactors.windSpeed">
									<text class="environment-label">Wind Speed</text>
									<text
										class="environment-value">{{ item.analysis.environmentalFactors.windSpeed }}</text>
								</view>
								<view class="environment-item" v-if="item.analysis.environmentalFactors.equipment">
									<text class="environment-label">Equipment</text>
									<text
										class="environment-value">{{ item.analysis.environmentalFactors.equipment }}</text>
								</view>
							</view>
							<view class="environment-impact">Impact Assessment:
								{{ item.analysis.environmentalFactors.impact }}</view>
						</view>

						<!-- 7. 运动效果与身体机能提升 -->
						<view v-if="item.analysis.fitnessImprovement" class="info-section">
							<text class="section-header">Fitness & Physical Improvement</text>
							<view class="fitness-grid">
								<view class="fitness-item" v-if="item.analysis.fitnessImprovement.vo2max">
									<text class="fitness-label">VO2 Max</text>
									<text class="fitness-value">{{ item.analysis.fitnessImprovement.vo2max }}</text>
								</view>
								<view class="fitness-item" v-if="item.analysis.fitnessImprovement.strength">
									<text class="fitness-label">Strength</text>
									<text class="fitness-value">{{ item.analysis.fitnessImprovement.strength }}</text>
								</view>
								<view class="fitness-item" v-if="item.analysis.fitnessImprovement.cardioHealth">
									<text class="fitness-label">Cardio Health</text>
									<text
										class="fitness-value">{{ item.analysis.fitnessImprovement.cardioHealth }}</text>
								</view>
								<view class="fitness-item" v-if="item.analysis.fitnessImprovement.muscleEndurance">
									<text class="fitness-label">Endurance</text>
									<text
										class="fitness-value">{{ item.analysis.fitnessImprovement.muscleEndurance }}</text>
								</view>
								<view class="fitness-item">
									<text class="fitness-label">Recovery</text>
									<text
										class="fitness-value">{{ item.analysis.fitnessImprovement.recoveryTime }}</text>
								</view>
							</view>
						</view>

						<!-- 8. 训练质量评价与建议 -->
						<view v-if="item.analysis.trainingQuality" class="info-section">
							<text class="section-header">Training Quality & Recommendations</text>
							<view class="quality-score">
								<text class="quality-label">Quality Score</text>
								<text class="quality-value">{{ item.analysis.trainingQuality.score }}pts</text>
							</view>
							<view class="quality-analysis">
								<text class="quality-subtitle">Strengths</text>
								<text v-for="(strength, index) in item.analysis.trainingQuality.strengths"
									:key="'strength-' + index" class="quality-point positive">• {{ strength }}</text>
							</view>
							<view class="quality-analysis">
								<text class="quality-subtitle">Improvements</text>
								<text v-for="(improvement, index) in item.analysis.trainingQuality.improvements"
									:key="'improvement-' + index" class="quality-point suggestion">•
									{{ improvement }}</text>
							</view>
							<text class="next-training">Next Training:
								{{ item.analysis.trainingQuality.nextTraining }}</text>
						</view>

						<!-- 9. 周计划安排 -->
						<view v-if="item.analysis.weeklyPlan" class="info-section">
							<text class="section-header">Weekly Plan</text>
							<view class="weekly-plan">
								<view class="plan-day">
									<text class="plan-day-name">Mon</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.monday }}</text>
								</view>
								<view class="plan-day">
									<text class="plan-day-name">Tue</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.tuesday }}</text>
								</view>
								<view class="plan-day">
									<text class="plan-day-name">Wed</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.wednesday }}</text>
								</view>
								<view class="plan-day">
									<text class="plan-day-name">Thu</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.thursday }}</text>
								</view>
								<view class="plan-day">
									<text class="plan-day-name">Fri</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.friday }}</text>
								</view>
								<view class="plan-day">
									<text class="plan-day-name">Sat</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.saturday }}</text>
								</view>
								<view class="plan-day">
									<text class="plan-day-name">Sun</text>
									<text class="plan-day-content">{{ item.analysis.weeklyPlan.sunday }}</text>
								</view>
							</view>
						</view>

						<!-- 10. 营养建议（一周）-->
						<view v-if="item.analysis.nutritionAdvice" class="info-section">
							<text class="section-header">Nutrition Advice (Weekly)</text>
							<view class="nutrition-timing">
								<view class="nutrition-item">
									<text class="nutrition-label">Pre-workout</text>
									<text
										class="nutrition-content">{{ item.analysis.nutritionAdvice.preWorkout }}</text>
								</view>
								<view class="nutrition-item">
									<text class="nutrition-label">During</text>
									<text
										class="nutrition-content">{{ item.analysis.nutritionAdvice.duringWorkout }}</text>
								</view>
								<view class="nutrition-item">
									<text class="nutrition-label">Post-workout</text>
									<text
										class="nutrition-content">{{ item.analysis.nutritionAdvice.postWorkout }}</text>
								</view>
							</view>
							<view class="weekly-nutrition">
								<text class="nutrition-subtitle">Weekly Focus</text>
								<text v-for="(tip, index) in item.analysis.nutritionAdvice.weeklyTips"
									:key="'tip-' + index" class="nutrition-tip">• {{ tip }}</text>
							</view>
						</view>

						<!-- 11. 原计划VS实际执行 -->
						<view v-if="item.analysis.planVsActual" class="info-section">
							<text class="section-header">Planned vs Actual</text>
							<view class="comparison-grid">
								<view class="comparison-item" v-if="item.analysis.planVsActual.plannedDistance">
									<text class="comparison-label">Distance</text>
									<text class="comparison-planned">Planned:
										{{ item.analysis.planVsActual.plannedDistance }}</text>
									<text class="comparison-actual">Actual:
										{{ item.analysis.planVsActual.actualDistance }}</text>
								</view>
								<view class="comparison-item" v-if="item.analysis.planVsActual.plannedTime">
									<text class="comparison-label">Time</text>
									<text class="comparison-planned">Planned:
										{{ item.analysis.planVsActual.plannedTime }}</text>
									<text class="comparison-actual">Actual:
										{{ item.analysis.planVsActual.actualTime }}</text>
								</view>
								<view class="comparison-item" v-if="item.analysis.planVsActual.plannedSets">
									<text class="comparison-label">Sets</text>
									<text class="comparison-planned">Planned:
										{{ item.analysis.planVsActual.plannedSets }}</text>
									<text class="comparison-actual">Actual:
										{{ item.analysis.planVsActual.actualSets }}</text>
								</view>
								<view class="comparison-item" v-if="item.analysis.planVsActual.plannedWeight">
									<text class="comparison-label">Weight</text>
									<text class="comparison-planned">Planned:
										{{ item.analysis.planVsActual.plannedWeight }}</text>
									<text class="comparison-actual">Actual:
										{{ item.analysis.planVsActual.actualWeight }}</text>
								</view>
							</view>
							<text class="completion-rate">Completion: {{ item.analysis.planVsActual.completion }}</text>
						</view>

						<!-- 12. 运动表现分析 -->
						<view v-if="item.analysis.performanceAnalysis" class="info-section">
							<text class="section-header">Performance Analysis</text>
							<view class="performance-grid">
								<view class="performance-item">
									<text class="performance-label">Technique</text>
									<text
										class="performance-value">{{ item.analysis.performanceAnalysis.technique }}</text>
								</view>
								<view class="performance-item">
									<text class="performance-label">Consistency</text>
									<text
										class="performance-value">{{ item.analysis.performanceAnalysis.consistency }}</text>
								</view>
								<view class="performance-item">
									<text class="performance-label">Endurance</text>
									<text
										class="performance-value">{{ item.analysis.performanceAnalysis.endurance }}</text>
								</view>
								<view class="performance-item">
									<text class="performance-label">Speed</text>
									<text
										class="performance-value">{{ item.analysis.performanceAnalysis.speed || item.analysis.performanceAnalysis.form }}</text>
								</view>
							</view>
						</view>

						<!-- 力量训练动作分析 -->
						<view v-if="item.analysis.exercises" class="info-section">
							<text class="section-header">Exercise Analysis</text>
							<view class="pace-list">
								<view v-for="(exercise, index) in item.analysis.exercises" :key="index"
									class="pace-row">
									<text class="pace-km">{{ exercise.name }}</text>
									<text class="pace-time">{{ exercise.sets }}sets {{ exercise.weight }}</text>
									<text class="pace-hr">Form {{ exercise.form }}</text>
								</view>
							</view>
						</view>

						<!-- AI助手对话记录 -->
						<view class="chat-section" v-if="false">
							<text class="section-header">Continue Consultation</text>

							<!-- 聊天记录 -->
							<scroll-view class="chat-messages" scroll-y="true" :scroll-top="scrollTop">
								<view v-for="(msg, index) in chatMessages[item.id] || []" :key="index" class="chat-item"
									:class="msg.type">
									<text class="chat-text">{{ msg.content }}</text>
								</view>
							</scroll-view>

							<!-- 聊天输入框 -->
							<view class="chat-input-container">
								<view class="chat-input-box">
									<uni-easyinput v-model="chatInput[item.id]" class="chat-input"
										placeholder="Ask questions about this workout..." :border="false"
										:clearable="false" @confirm="sendMessage(item.id)"></uni-easyinput>
									<view class="send-icon-btn"
										:class="{ disabled: !chatInput[item.id] || !chatInput[item.id].trim() }"
										@click="sendMessage(item.id)">
										<text class="send-icon">➤</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'
	export default {
		components: {},
		data() {
			return {
				statusBarHeight: 0,
				loading: false,
				selectedDate: new Date(),
				weekDays: [],
				scrollLeft: 0,
				analysisData: [],
				expandedItems: {},
				chatMessages: {},
				chatInput: {},
				scrollTop: 0,
				// 手势相关
				touchStartX: 0,
				touchEndX: 0,
				currentWeekOffset: 0,
				// 轮询相关
				pollingTimer: null,
				isPollingActive: false
			}
		},
		computed: {
			hasAnalysisData() {
				return this.analysisData && this.analysisData.length > 0;
			},

			// 当前月份文本
			currentMonthText() {
				const date = this.selectedDate;
				return date.toLocaleDateString('en-US', {
					month: 'long'
				});
			},

			// 当前日期字符串（用于picker组件）
			currentDateString() {
				const date = this.selectedDate;
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			}
		},
		onLoad(options) {
			// 获取状态栏高度
			try {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			} catch (e) {
				this.statusBarHeight = 0;
			}

			this.initWeekDays();

			// 检查是否有analysisId参数
			if (options && options.analysisId) {
				// 如果有analysisId，直接加载该分析的详细信息
				this.loadAnalysisDetail(options.analysisId);
			} else {
				// 否则加载当前日期的分析数据
				this.loadAnalysisData();
			}
		},

		// 页面显示时（包括从其他页面跳转过来）刷新数据，避免分析记录未创建的问题
		onShow() {
			// 不干扰从analysisId进入的页面加载逻辑
			if (!this.$mp || !this.$mp.query || !this.$mp.query.analysisId) {
				this.analysisData = [];
				this.expandedItems = {};
				this.loadAnalysisData();
			}
		},

		// 页面卸载时停止轮询
		onUnload() {
			this.stopPolling();
		},
		methods: {
			// 初始化7天日期数据
			initWeekDays() {
				const today = new Date();
				const weekDays = [];

				// 基于当前偏移量计算起始日期
				const startDate = new Date(today);
				startDate.setDate(today.getDate() + (this.currentWeekOffset * 7));

				for (let i = -3; i <= 3; i++) {
					const date = new Date(startDate);
					date.setDate(startDate.getDate() + i);

					const weekDay = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][date.getDay()];

					weekDays.push({
						date: date,
						weekDay: weekDay,
						day: date.getDate()
					});
				}

				this.weekDays = weekDays;
			},

			// 基于选中日期初始化一周的日期数据
			initWeekDaysForSelectedDate() {
				const weekDays = [];
				const selectedDate = new Date(this.selectedDate);

				// 以选中日期为中心，生成前后3天的日期
				for (let i = -3; i <= 3; i++) {
					const date = new Date(selectedDate);
					date.setDate(selectedDate.getDate() + i);

					const weekDay = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][date.getDay()];

					weekDays.push({
						date: date,
						weekDay: weekDay,
						day: date.getDate()
					});
				}

				this.weekDays = weekDays;
			},

			// 显示日期选择器
			showDatePicker() {
				// 这个方法保留作为备用，现在使用picker组件
				console.log('日期选择器被触发');
			},

			// 日期选择器变化事件
			onDateChange(e) {
				const selectedDateStr = e.detail.value;
				const selectedDate = new Date(selectedDateStr);

				// 更新选中日期
				this.selectedDate = selectedDate;

				// 计算新日期相对于今天的偏移量（以周为单位）
				const today = new Date();
				today.setHours(0, 0, 0, 0); // 重置为当天开始时间
				selectedDate.setHours(0, 0, 0, 0); // 重置为选中日期开始时间

				const diffInDays = Math.floor((selectedDate - today) / (1000 * 60 * 60 * 24));
				this.currentWeekOffset = Math.floor(diffInDays / 7);

				// 重新初始化一周的日期，确保选中日期在显示范围内
				this.initWeekDaysForSelectedDate();

				// 加载新的分析数据
				this.loadAnalysisData();
			},

			// 处理触摸开始
			handleTouchStart(e) {
				this.touchStartX = e.touches[0].clientX;
			},

			// 处理触摸移动
			handleTouchMove(e) {
				// 阻止默认滑动行为，防止整个页面滑动
				e.preventDefault();
				e.stopPropagation();
			},

			// 处理触摸结束
			handleTouchEnd(e) {
				this.touchEndX = e.changedTouches[0].clientX;
				const deltaX = this.touchEndX - this.touchStartX;

				// 滑动距离大于50px才触发切换
				if (Math.abs(deltaX) > 50) {
					if (deltaX > 0) {
						// 向右滑动，显示上一周
						this.previousWeek();
					} else {
						// 向左滑动，显示下一周
						this.nextWeek();
					}
				}
			},

			// 上一周
			previousWeek() {
				this.currentWeekOffset--;

				// 更新选中日期为新周的中间日期
				const today = new Date();
				const newSelectedDate = new Date(today);
				newSelectedDate.setDate(today.getDate() + (this.currentWeekOffset * 7));
				this.selectedDate = newSelectedDate;

				// 初始化新的一周日期
				this.initWeekDays();
				this.loadAnalysisData();
			},

			// 下一周
			nextWeek() {
				this.currentWeekOffset++;

				// 更新选中日期为新周的中间日期
				const today = new Date();
				const newSelectedDate = new Date(today);
				newSelectedDate.setDate(today.getDate() + (this.currentWeekOffset * 7));
				this.selectedDate = newSelectedDate;

				// 初始化新的一周日期
				this.initWeekDays();
				this.loadAnalysisData();
			},

			// 选择日期
			selectDate(date) {
				this.selectedDate = date;
				// 清空之前的数据，重新加载
				this.analysisData = [];
				this.expandedItems = {};
				this.loadAnalysisData();
			},

			// 判断是否为同一天
			isSameDay(date1, date2) {
				if (!date1 || !date2) return false;
				const d1 = new Date(date1.getTime());
				const d2 = new Date(date2.getTime());
				return d1.getFullYear() === d2.getFullYear() &&
					d1.getMonth() === d2.getMonth() &&
					d1.getDate() === d2.getDate();
			},

			// 格式化日期显示
			formatDate(date) {
				return date.toLocaleDateString('en-US', {
					month: 'long',
					day: 'numeric'
				});
			},

			// 加载分析数据
			loadAnalysisData() {
				this.loading = true;

				try {
					// 构建查询参数，传递选中的日期
					const queryParams = {
						date: this.selectedDate
					};

					// 调用API获取分析数据，传递日期参数
					api.exerciseAnalysis.getAnalyses(queryParams)
						.then(res => {
							if (res.success) {
								// 将API返回的数据转换为页面所需的格式
								this.analysisData = this.transformApiData(res.data.analyses);

							} else {
								console.error('获取分析数据失败:', res.message);
								this.analysisData = [];
							}
						})
						.catch(error => {
							console.error('获取分析数据出错:', error);
							this.analysisData = [];
						})
						.finally(() => {
							this.loading = false;

							// 数据加载完成后，检查是否有分析中的项目，如果有则启动轮询
							const hasAnalyzingItems = this.analysisData.some(item =>
								item.status === 'analyzing' || item.status === 'pending'
							);

							if (hasAnalyzingItems) {
								this.startPolling();
							} else {
								this.stopPolling();
							}
						});
				} catch (error) {
					console.error('数据加载失败:', error);
					this.analysisData = [];
					this.loading = false;
				}
			},

			// 加载单个分析详情
			loadAnalysisDetail(analysisId) {
				this.loading = true;
				console.info('loadAnalysisDetail', analysisId)
				try {
					api.exerciseAnalysis.getAnalysisDetail(analysisId)
						.then(res => {
							if (res.success && res.data && res.data.analysis) {
								// 将单个分析详情转换为页面所需的格式，并添加到analysisData数组中
								const transformedData = this.transformSingleAnalysisData(res.data.analysis);
								this.analysisData = [transformedData];

								// 默认展开第一个分析项
								if (this.analysisData.length > 0) {
									this.$set(this.expandedItems, this.analysisData[0].id, true);
								}
							} else {
								console.error('获取分析详情失败:', res.message);
								this.analysisData = [];
							}
						})
						.catch(error => {
							console.error('获取分析详情出错:', error);
							this.analysisData = [];
						})
						.finally(() => {
							this.loading = false;

							// 数据加载完成后，检查是否有分析中的项目，如果有则启动轮询
							const hasAnalyzingItems = this.analysisData.some(item =>
								item.status === 'analyzing' || item.status === 'pending'
							);

							if (hasAnalyzingItems) {
								this.startPolling();
							} else {
								this.stopPolling();
							}
						});
				} catch (error) {
					console.error('加载分析详情失败:', error);
					this.analysisData = [];
					this.loading = false;
				}
			},

			// 转换API返回的分析列表数据为页面所需格式
			transformApiData(analyses) {
				if (!analyses || !Array.isArray(analyses)) {
					return [];
				}

				return analyses.map(analysis => {
					return this.transformSingleAnalysisData(analysis);
				});
			},

			// 转换单个分析数据为页面所需格式
			transformSingleAnalysisData(analysis) {
				console.info('transformSingleAnalysisData', analysis)
				// 从analysis_result中提取各个分析部分
				const result = analysis.analysis_result || {};
				console.info('transformSingleAnalysisData result', result)
				// 获取分析状态
				const status = analysis.status || 'pending';
				// 根据analysis_result的结构创建页面所需的数据格式
				// 注意：这里需要根据后端实际返回的analysis_result结构进行调整
				return {
					id: analysis.id,
					type: analysis.type,
					time: this.formatTimeFromDate(analysis.created_at),
					score: analysis.score || 0,
					duration: result.duration || '',
					distance: result.distance || '',
					calories: result.calories || '',
					avgPace: result.avgPace,
					targetMuscle: '',
					images: analysis.image_urls || [],
					status: status,
					analysis: result.analysis
				};
			},

			// 从日期字符串中提取时间部分
			formatTimeFromDate(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				return `${hours}:${minutes}`;
			},

			// 生成模拟数据
			generateMockData() {
				const mockData = [{
						id: 1,
						type: 'Morning Run',
						time: '07:30',
						score: 85,
						duration: '35 min',
						distance: '5.2 km',
						calories: '312 kcal',
						avgPace: '6\'45"',
						maxHR: 165,
						images: [
							'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
							'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
							'https://images.unsplash.com/photo-1571019614242-c5c5dee9f50b?w=400'
						],
						analysis: {
							overallPerformance: 'Great morning run performance! Pace improved by 15 seconds compared to yesterday, heart rate well controlled. Recommend maintaining this intensity.',
							paceAnalysis: [{
									km: 1,
									pace: '6\'32"',
									heartRate: '145bpm'
								},
								{
									km: 2,
									pace: '6\'45"',
									heartRate: '152bpm'
								},
								{
									km: 3,
									pace: '6\'48"',
									heartRate: '158bpm'
								},
								{
									km: 4,
									pace: '6\'52"',
									heartRate: '162bpm'
								},
								{
									km: 5,
									pace: '7\'05"',
									heartRate: '165bpm'
								}
							],
							heartRateZones: [{
									zone: 'Warm-up (50-60%)',
									time: '5 min',
									percentage: '14%',
									description: 'Cool-down phase'
								},
								{
									zone: 'Aerobic (60-70%)',
									time: '20 min',
									percentage: '57%',
									description: 'Main training phase'
								},
								{
									zone: 'Anaerobic (70-85%)',
									time: '10 min',
									percentage: '29%',
									description: 'High-intensity sprint'
								}
							],
							calorieMetabolism: {
								totalCalories: 312,
								fatBurn: '65%',
								carbohydrateBurn: '35%',
								metabolicRate: 'Increased 25%',
								afterburnEffect: 'Lasts 6 hours'
							},
							runningEfficiency: {
								avgCadence: '168 steps/min',
								strideLength: '1.82m',
								efficiency: '87%',
								improvement: '3% better than last week'
							},
							environmentalFactors: {
								temperature: '18°C',
								humidity: '65%',
								windSpeed: '2.1m/s',
								impact: 'Good, suitable for running'
							},
							fitnessImprovement: {
								vo2max: '45.2ml/kg/min',
								cardioHealth: 'Excellent',
								muscleEndurance: 'Improved 8%',
								recoveryTime: '24 hours'
							},
							trainingQuality: {
								score: 85,
								strengths: ['Stable pace', 'Good heart rate control'],
								improvements: ['Add interval training', 'Pay attention to hydration'],
								nextTraining: 'Schedule recovery run tomorrow'
							},
							weeklyPlan: {
								monday: 'Rest day',
								tuesday: 'Interval run 5x800m',
								wednesday: 'Recovery run 30min',
								thursday: 'Strength training',
								friday: 'Tempo run 40min',
								saturday: 'Long distance slow run',
								sunday: 'Cross training'
							},
							nutritionAdvice: {
								preWorkout: 'Oatmeal + banana, 1 hour before training',
								duringWorkout: 'Hydrate 150ml every 15min',
								postWorkout: 'Protein + carbs within 30min',
								weeklyTips: ['Increase omega-3 intake', 'Ensure adequate protein',
									'Eat more anti-inflammatory foods'
								]
							},
							planVsActual: {
								plannedDistance: '5.0 km',
								actualDistance: '5.2 km',
								plannedTime: '35 min',
								actualTime: '35 min',
								completion: '104%'
							},
							performanceAnalysis: {
								technique: 'Running form 85% standard',
								consistency: 'Good pace stability',
								endurance: 'Excellent endurance performance',
								speed: 'Sufficient speed reserve'
							}
						}
					},
					{
						id: 2,
						type: 'Strength Training',
						time: '19:00',
						score: 92,
						duration: '45 min',
						sets: 12,
						calories: '285 kcal',
						targetMuscle: 'Chest, Triceps',
						images: [
							'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=400',
							'https://images.unsplash.com/photo-1571388208497-71bedc66e932?w=400'
						],
						analysis: {
							overallPerformance: 'Excellent strength training session! All exercises performed with high form standards and proper weight progression. Recommend aerobic recovery training tomorrow.',
							exercises: [{
									name: 'Bench Press',
									sets: 4,
									reps: '12,10,8,6',
									weight: '60kg',
									form: '95%'
								},
								{
									name: 'Dumbbell Flyes',
									sets: 3,
									reps: '12,12,10',
									weight: '15kg',
									form: '92%'
								},
								{
									name: 'Tricep Pushdowns',
									sets: 3,
									reps: '15,12,12',
									weight: '25kg',
									form: '88%'
								}
							],
							heartRateZones: [{
									zone: 'Recovery (50-60%)',
									time: '15 min',
									percentage: '33%',
									description: 'Inter-set rest'
								},
								{
									zone: 'Strength (60-80%)',
									time: '25 min',
									percentage: '56%',
									description: 'Main training'
								},
								{
									zone: 'High Intensity (80%+)',
									time: '5 min',
									percentage: '11%',
									description: 'Max weight sets'
								}
							],
							calorieMetabolism: {
								totalCalories: 285,
								fatBurn: '45%',
								carbohydrateBurn: '55%',
								metabolicRate: 'Increased 30%',
								afterburnEffect: 'Lasts 8 hours'
							},
							trainingEfficiency: {
								volumeLoad: '3420kg',
								intensity: '82%',
								restTime: 'Avg 90 seconds',
								efficiency: '91%'
							},
							environmentalFactors: {
								temperature: '22°C',
								humidity: '45%',
								equipment: 'Equipment in good condition',
								impact: 'Optimal training environment'
							},
							fitnessImprovement: {
								strength: '5% increase vs last week',
								muscleGrowth: 'Expected growth',
								powerOutput: 'Significant improvement',
								recoveryTime: '48 hours'
							},
							trainingQuality: {
								score: 92,
								strengths: ['Standard form', 'Weight progression'],
								improvements: ['Increase training frequency', 'Focus on stretching'],
								nextTraining: 'Lower body training tomorrow'
							},
							weeklyPlan: {
								monday: 'Chest + Triceps',
								tuesday: 'Back + Biceps',
								wednesday: 'Legs + Glutes',
								thursday: 'Shoulders + Core',
								friday: 'Full body functional',
								saturday: 'Cardio recovery',
								sunday: 'Rest + Stretching'
							},
							nutritionAdvice: {
								preWorkout: 'Coffee + light carbs, 30min before training',
								duringWorkout: 'Electrolyte supplement',
								postWorkout: 'Whey protein + banana within 30min',
								weeklyTips: ['Increase protein to 1.6g/kg', 'Ensure adequate sleep',
									'Add creatine supplement'
								]
							},
							planVsActual: {
								plannedSets: '12 sets',
								actualSets: '12 sets',
								plannedWeight: 'Avg 55kg',
								actualWeight: 'Avg 58kg',
								completion: '105%'
							},
							performanceAnalysis: {
								technique: 'Exercise execution 91% standard',
								progression: 'Reasonable weight increase',
								endurance: 'Good muscle endurance',
								form: 'High movement quality throughout'
							}
						}
					}
				];

				return mockData;
			},

			// 图片预览
			previewImage(current, images) {
				uni.previewImage({
					current: current,
					urls: images
				});
			},

			// 切换展开/收起状态
			toggleExpand(itemId) {
				// 找到对应的分析项
				const item = this.analysisData.find(item => item.id === itemId);
				// 只有completed状态的分析才能展开
				if (item && item.status === 'completed') {
					// 如果当前是展开状态，直接收起
					if (this.expandedItems[itemId]) {
						this.$set(this.expandedItems, itemId, false);
					} else {
						// 如果当前是收起状态，先收起所有其他项，然后调用详情接口获取最新数据并展开
						// 收起所有其他项
						Object.keys(this.expandedItems).forEach(id => {
							this.$set(this.expandedItems, id, false);
						});

						// 调用详情接口获取最新数据
						this.loadDetailedAnalysisData(itemId);
					}
				}
			},

			// 加载详细的分析数据
			loadDetailedAnalysisData(analysisId) {
				this.loading = true;

				try {
					api.exerciseAnalysis.getAnalysisDetail(analysisId)
						.then(res => {
							if (res.success && res.data && res.data.analysis) {
								// 将获取到的详细数据更新到analysisData中
								const updatedItem = this.transformSingleAnalysisData(res.data.analysis);
								console.info('updatedItem', updatedItem)
								const index = this.analysisData.findIndex(item => item.id === analysisId);
								console.info('getAnalysisDetail index', index)
								if (index !== -1) {
									// 更新现有项
									this.$set(this.analysisData, index, updatedItem);
								} else {
									// 如果找不到该项，则添加到数组
									this.analysisData.push(updatedItem);
								}

								// 展开该项
								this.$set(this.expandedItems, analysisId, true);
							} else {
								console.error('获取分析详情失败:', res.message);
							}
						})
						.catch(error => {
							console.error('获取分析详情出错:', error);
						})
						.finally(() => {
							this.loading = false;
						});
				} catch (error) {
					console.error('加载分析详情失败:', error);
					this.loading = false;
				}
			},

			// 启动轮询检查分析状态
			startPolling() {
				if (this.isPollingActive) {
					return;
				}

				this.isPollingActive = true;
				// 每5秒检查一次分析状态
				this.pollingTimer = setInterval(() => {
					this.checkAnalysisStatus();
				}, 5000);
			},

			// 停止轮询
			stopPolling() {
				if (this.pollingTimer) {
					clearInterval(this.pollingTimer);
					this.pollingTimer = null;
					this.isPollingActive = false;
				}
			},

			// 检查分析状态
			checkAnalysisStatus() {
				// 检查是否有分析中的项目
				const hasAnalyzingItems = this.analysisData.some(item =>
					item.status === 'analyzing' || item.status === 'pending'
				);

				if (hasAnalyzingItems) {
					// 重新加载数据以更新状态
					this.loadAnalysisData();
				} else {
					// 如果没有分析中的项目，停止轮询
					this.stopPolling();
				}
			},

			// 发送AI消息
			sendMessage(itemId) {
				const message = this.chatInput[itemId];
				if (!message || !message.trim()) return;

				// 添加用户消息
				if (!this.chatMessages[itemId]) {
					this.$set(this.chatMessages, itemId, []);
				}

				this.chatMessages[itemId].push({
					type: 'user',
					content: message
				});

				// 清空输入框
				this.$set(this.chatInput, itemId, '');

				// 模拟AI回复
				setTimeout(() => {
					const aiResponses = [
						'Based on your heart rate zone distribution, I recommend adding more aerobic zone training to improve cardiovascular efficiency.',
						'Your pace control is excellent! Consider trying interval training next time to improve speed reserves.',
						'From the metabolism analysis, your fat burning ratio is ideal. Maintaining this intensity will help with fat loss.',
						'Your cadence data shows good stability. Consider slightly increasing stride length to improve running efficiency.',
						'Environmental factors are perfect for training. Temperature and humidity are in optimal range, you can slightly increase training intensity.',
						'Your training quality score is very high. Follow the weekly plan and pay attention to recovery.',
						'Nutrition timing looks great. Consider adding some anti-inflammatory foods to accelerate recovery.',
						'Completion rate over 100% - excellent work! Consider adjusting goals next time to challenge yourself.',
						'Form standards are very high. Consider increasing weight slightly or adding more sets next time.',
						'Focus on stretching and massage during recovery to help muscle recovery and prevent injury.'
					];

					const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];

					this.chatMessages[itemId].push({
						type: 'ai',
						content: randomResponse
					});

					// 滚动到底部
					this.$nextTick(() => {
						this.scrollTop = 1000;
					});
				}, 1000);
			}
		}
	}
</script>

<style>
	.page {
		flex: 1;
		background-color: #f5f5f5;
		width: 100%;
		overflow-x: hidden;
		box-sizing: border-box;
	}

	.status-bar {
		background-color: #667eea;
	}

	/* 导航栏 */
	.nav-bar {
		height: 88rpx;
		background-color: #667eea;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding-left: 30rpx;
		padding-right: 30rpx;
	}

	.nav-title {
		font-size: 36rpx;
		color: #ffffff;
		font-weight: bold;
	}


	/* 月份显示和日历图标 */
	.month-header {
		background-color: #ffffff;
		padding: 20rpx 32rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #f3f4f6;
	}

	.month-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #111827;
		flex: 1;
	}

	.calendar-icon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		flex-shrink: 0;
	}

	.date-picker {
		display: flex;
	}

	.icon-calendar {
		font-size: 32rpx;
	}

	/* 7天日期选择器 */
	.week-selector {
		background-color: #ffffff;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #e5e7eb;
		overflow: hidden;
	}

	.week-scroll {
		height: 120rpx;
		width: 100%;
	}

	.week-container {
		display: flex;
		flex-direction: row;
		padding: 0 20rpx;
		align-items: center;
		white-space: nowrap;
	}

	.day-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 80rpx;
		height: 100rpx;
		margin-right: 20rpx;
		border-radius: 12rpx;
		background-color: #f9fafb;
		flex-shrink: 0;
	}

	.day-item.day-active {
		background-color: #667eea;
	}

	.day-week {
		font-size: 24rpx;
		color: #6b7280;
		margin-bottom: 8rpx;
	}

	.day-number {
		font-size: 28rpx;
		font-weight: 600;
		color: #111827;
	}

	.day-active .day-week,
	.day-active .day-number {
		color: #ffffff;
	}

	/* 主内容区域 */
	.main-content {
		flex: 1;
	}

	/* 加载和空状态 */
	.loading,
	.empty {
		flex: 1;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #6b7280;
	}

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 24rpx;
	}

	.empty-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #111827;
		margin-bottom: 12rpx;
	}

	.empty-desc {
		font-size: 28rpx;
		color: #6b7280;
		text-align: center;
	}

	/* 分析列表 */
	.analysis-scroll {
		flex: 1;
		padding: 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.analysis-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		max-width: 100%;
		box-sizing: border-box;
	}

	/* 项目头部 */
	.item-header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
		border-bottom: 1rpx solid #f3f4f6;
	}

	.item-info {
		flex: 1;
	}

	.item-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #111827;
		margin-bottom: 8rpx;
	}

	.item-time {
		font-size: 26rpx;
		color: #6b7280;
	}

	.item-score {
		display: flex;
		flex-direction: row;
		align-items: baseline;
		margin-right: 20rpx;
	}

	.score-num {
		font-size: 36rpx;
		font-weight: bold;
		color: #10b981;
		margin-right: 6rpx;
	}

	.score-text {
		font-size: 24rpx;
		color: #666;
	}

	.expand-icon {
		font-size: 32rpx;
		color: #666;
		width: 40rpx;
		text-align: center;
	}

	/* 状态显示容器 */
	.status-container {
		display: flex;
		align-items: center;
		margin-right: 16rpx;
	}

	/* 状态样式 */
	.status {
		display: flex;
		align-items: center;
		padding: 8rpx 18rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
	}

	/* 待分析状态 */
	.status.pending {
		background-color: #f5f5f5;
		color: #666;
	}

	/* 分析中状态 */
	.status.analyzing {
		background-color: #e9e9e9;
		color: #555;
	}

	/* 已完成状态 */
	.status.completed {
		background-color: #e0e0e0;
		color: #444;
	}

	/* 分析失败状态 */
	.status.failed {
		background-color: #f0f0f0;
		color: #666;
	}

	/* 旋转动画 - 分析中状态 */
	.spinner {
		width: 24rpx;
		height: 24rpx;
		border: 3rpx solid rgba(100, 100, 100, 0.2);
		border-radius: 50%;
		border-top: 3rpx solid #666;
		margin-right: 8rpx;
		animation: spin 1s linear infinite;
	}

	/* 旋转动画定义 */
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	/* 报告内图片区域 */
	.report-images-section {
		padding: 0 20rpx 20rpx 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.images-section-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 14rpx;
	}

	/* 报告内图片网格布局 */
	.report-images-grid {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: flex-start;
		width: 100%;
		gap: 14rpx;
	}

	.report-image-item {
		width: calc(33.33% - 9.33rpx);
		height: 190rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		margin-bottom: 16rpx;
		position: relative;
		box-sizing: border-box;
	}

	.report-image-item-last {
		margin-right: 0;
	}

	.report-image {
		width: 100%;
		height: 190rpx;
		background-color: #f5f5f5;
	}

	/* 基础统计 */
	.basic-stats {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		padding: 18rpx 20rpx;
		border-bottom: 1rpx solid #e0e0e0;
		justify-content: space-between;
		width: 100%;
		box-sizing: border-box;
		margin-bottom: 22rpx;
	}

	.stat-item {
		width: calc(33.33% - 10rpx);
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 18rpx;
		box-sizing: border-box;
	}

	.stat-value {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 6rpx;
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
	}

	/* 详细信息 */
	.detailed-info {
		padding: 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.info-section {
		margin-bottom: 28rpx;
	}

	.section-header {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.section-content {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-top: 4rpx;
	}

	/* 配速列表 */
	.pace-list {
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.pace-row {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 14rpx 18rpx;
		border-bottom: 1rpx solid #e0e0e0;
		background-color: #f9f9f9;
	}

	.pace-row:last-child {
		border-bottom: none;
	}

	.pace-km,
	.pace-time,
	.pace-hr {
		font-size: 26rpx;
		color: #333;
		flex: 1;
		text-align: center;
	}

	/* 心率区间 */
	.hr-zones {
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.hr-zone {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 14rpx 18rpx;
		border-bottom: 1rpx solid #e0e0e0;
		background-color: #f9f9f9;
	}

	.hr-zone:last-child {
		border-bottom: none;
	}

	.zone-name,
	.zone-time,
	.zone-percent {
		font-size: 26rpx;
		color: #333;
		flex: 1;
		text-align: center;
	}

	.zone-desc {
		font-size: 22rpx;
		color: #666;
		flex: 1;
		text-align: center;
	}

	/* 通用两列网格布局 - 应用于所有网格容器 */
	.metabolism-grid,
	.efficiency-grid,
	.environment-grid,
	.fitness-grid,
	.comparison-grid,
	.performance-grid,
	.nutrition-timing {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		gap: 12rpx;
		justify-content: space-between;
	}

	/* 通用网格项目 - 应用于所有网格项 */
	.metabolism-item,
	.efficiency-item,
	.environment-item,
	.fitness-item,
	.comparison-item,
	.performance-item,
	.nutrition-item {
		width: calc(50% - 6rpx);
		padding: 16rpx 14rpx;
		background-color: #f5f5f5;
		border-radius: 8rpx;
		border: 1rpx solid #e0e0e0;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-sizing: border-box;
	}

	/* 通用标签样式 - 应用于所有标签 */
	.metabolism-label,
	.efficiency-label,
	.environment-label,
	.fitness-label,
	.performance-label {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 6rpx;
	}

	/* 通用值样式 - 应用于所有值 */
	.metabolism-value,
	.efficiency-value,
	.environment-value,
	.fitness-value,
	.performance-value {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
	}

	/* 特殊背景色 - 热量代谢分析 */
	.metabolism-item {
		background-color: #f9f9f9;
	}

	/* 环境因素特殊设置 */
	.environment-grid {
		margin-bottom: 14rpx;
	}

	.environment-impact {
		font-size: 26rpx;
		color: #444;
		font-weight: 500;
		text-align: center;
		padding: 14rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
		border: 1rpx solid #e0e0e0;
	}

	/* 训练质量评价 */
	.quality-score {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 18rpx 20rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
		margin-bottom: 18rpx;
	}

	.quality-label {
		font-size: 28rpx;
		color: #444;
		font-weight: 600;
	}

	.quality-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.quality-analysis {
		margin-bottom: 14rpx;
	}

	.quality-subtitle {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.quality-point {
		font-size: 24rpx;
		line-height: 1.6;
		margin-bottom: 6rpx;
		display: block;
	}

	.quality-point.positive {
		color: #444;
	}

	.quality-point.suggestion {
		color: #666;
	}

	.next-training {
		font-size: 26rpx;
		color: #444;
		font-weight: 500;
		text-align: center;
		padding: 14rpx;
		background-color: #f5f5f5;
		border-radius: 8rpx;
	}

	/* 周计划安排 */
	.weekly-plan {
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.plan-day {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 14rpx 18rpx;
		border-bottom: 1rpx solid #e0e0e0;
		background-color: #f9f9f9;
	}

	.plan-day:last-child {
		border-bottom: none;
	}

	.plan-day-name {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		width: 80rpx;
		flex-shrink: 0;
	}

	.plan-day-content {
		font-size: 26rpx;
		color: #666;
		flex: 1;
		margin-left: 20rpx;
	}

	/* 营养建议 */
	.nutrition-timing {
		margin-bottom: 22rpx;
	}

	.nutrition-item {
		padding: 14rpx 18rpx;
		margin-bottom: 0;
	}

	.nutrition-label {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 8rpx;
		text-align: center;
	}

	.nutrition-content {
		font-size: 24rpx;
		color: #666;
		line-height: 1.5;
		text-align: center;
	}

	.weekly-nutrition {
		padding: 18rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
		border: 1rpx solid #e0e0e0;
	}

	.nutrition-subtitle {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.nutrition-tip {
		font-size: 24rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 6rpx;
		display: block;
	}

	/* 计划对比 */
	.comparison-grid {
		margin-bottom: 18rpx;
	}

	.comparison-item {
		padding: 14rpx 18rpx;
		margin-bottom: 0;
	}

	.comparison-label {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 6rpx;
		display: block;
	}

	.comparison-planned,
	.comparison-actual {
		font-size: 24rpx;
		margin-bottom: 3rpx;
		display: block;
	}

	.comparison-planned {
		color: #666;
	}

	.comparison-actual {
		color: #333;
		font-weight: 500;
	}

	.completion-rate {
		font-size: 28rpx;
		font-weight: bold;
		color: #444;
		text-align: center;
		padding: 14rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
	}

	/* 运动表现分析 */
	/* 已在通用样式中定义，此处无需重复 */

	/* 聊天区域 */
	.chat-section {
		margin-top: 22rpx;
	}

	.chat-messages {
		max-height: 400rpx;
		margin-bottom: 18rpx;
		display: flex;
		flex-direction: column;
	}

	.chat-item {
		margin-bottom: 14rpx;
		padding: 14rpx 18rpx;
		border-radius: 16rpx;
		max-width: 80%;
	}

	.chat-item.user {
		align-self: flex-end;
		background-color: #444;
		margin-left: 20%;
	}

	.chat-item.ai {
		align-self: flex-start;
		background-color: #f0f0f0;
		margin-right: 20%;
	}

	.chat-item.user .chat-text {
		color: #ffffff;
	}

	.chat-item.ai .chat-text {
		color: #333;
	}

	.chat-text {
		font-size: 26rpx;
		line-height: 1.5;
	}

	/* 聊天输入区域 */
	.chat-input-container {
		background-color: #f5f5f5;
		border-radius: 12rpx;
		padding: 14rpx;
	}

	.chat-input-box {
		display: flex;
		flex-direction: row;
		align-items: center;
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 8rpx 16rpx;
		border: 1rpx solid #e0e0e0;
		height: 72rpx;
		box-sizing: border-box;
	}

	.chat-input {
		flex: 1;
		margin-right: 12rpx;
		background-color: transparent !important;
	}

	.send-icon-btn {
		width: 56rpx;
		height: 56rpx;
		background-color: #555;
		border-radius: 28rpx;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		display: flex;
	}

	.send-icon-btn.disabled {
		background-color: #d0d0d0;
		opacity: 0.6;
	}

	.send-icon {
		color: #ffffff;
		font-size: 24rpx;
		font-weight: bold;
		line-height: 24rpx;
	}
</style>