<template>
  <view class="personal-details">
    <uni-forms ref="form" :model="formData" :rules="rules" label-position="top">
      <!-- 基本信息 -->
      <uni-section :title="$t('settings.basicInfo')" type="line">
        <uni-forms-item :label="$t('settings.name')" name="name" required>
          <uni-easyinput 
            v-model="formData.name" 
            :placeholder="$t('settings.namePlaceholder')"
            :clearable="true"
          />
        </uni-forms-item>
        
        <uni-forms-item :label="$t('settings.email')" name="email" required>
          <uni-easyinput 
            v-model="formData.email" 
            :placeholder="$t('settings.emailPlaceholder')"
            type="email"
            :disabled="true"
          />
        </uni-forms-item>
        
        <uni-forms-item :label="$t('settings.gender')" name="gender">
          <uni-data-select 
            v-model="formData.gender" 
            :localdata="genderOptions"
            :placeholder="$t('settings.genderPlaceholder')"
            :clear="true"
          />
        </uni-forms-item>
        
        <uni-forms-item :label="$t('settings.birthday')" name="birthday">
          <uni-datetime-picker 
            v-model="formData.birthday"
            type="date"
            :end="maxDate"
            :placeholder="$t('settings.birthdayPlaceholder')"
            :clearIcon="true"
          />
        </uni-forms-item>
      </uni-section>
      
      <!-- 身体信息 -->
      <uni-section :title="$t('settings.bodyInfo')" type="line">
        <uni-forms-item :label="$t('settings.height')" name="height">
          <uni-easyinput 
            v-model="formData.height" 
            :placeholder="$t('settings.heightPlaceholder')"
            type="number"
            :clearable="true"
          />
        </uni-forms-item>
        
        <uni-forms-item :label="$t('settings.weight')" name="weight">
          <uni-easyinput 
            v-model="formData.weight" 
            :placeholder="$t('settings.weightPlaceholder')"
            type="number"
            :clearable="true"
          />
        </uni-forms-item>
        
        <uni-forms-item v-if="bmi" :label="$t('settings.bmi')">
          <view class="bmi-display">
            <text class="bmi-value">{{ bmi }}</text>
            <text class="bmi-status">{{ bmiStatus }}</text>
          </view>
        </uni-forms-item>
      </uni-section>
      
      <!-- 运动信息 -->
      <uni-section :title="$t('settings.fitnessInfo')" type="line">
        <uni-forms-item :label="$t('settings.exerciseFrequency')" name="exercise_frequency">
          <uni-data-select 
            v-model="formData.exercise_frequency" 
            :localdata="exerciseFrequencyOptions"
            :placeholder="$t('settings.exerciseFrequencyPlaceholder')"
            :clear="true"
          />
        </uni-forms-item>
        
        <uni-forms-item :label="$t('settings.fitnessGoals')" name="fitness_goals">
          <uni-data-checkbox 
            v-model="formData.fitness_goals" 
            :localdata="fitnessGoalsOptions"
            mode="tag"
            multiple
          />
        </uni-forms-item>
        
        <uni-forms-item :label="$t('settings.obstacles')" name="obstacles">
          <uni-data-checkbox 
            v-model="formData.obstacles" 
            :localdata="obstaclesOptions"
            mode="tag"
            multiple
          />
        </uni-forms-item>
      </uni-section>
    </uni-forms>
    
    <view class="btn-group">
      <button type="primary" class="submit-btn" @click="handleSubmit">{{ $t('settings.updateProfile') }}</button>
    </view>
  </view>
</template>

<script>
import api from '@/common/api.js'
import { store, mutations } from '@/common/store.js'

export default {
  data() {
    return {
      formData: {
        name: '',
        email: '',
        gender: '',
        birthday: '',
        height: '',
        weight: '',
        exercise_frequency: '',
        fitness_goals: [],
        obstacles: []
      },
      rules: {
        name: [
          {
            required: true,
            errorMessage: this.$t('settings.namePlaceholder')
          }
        ],
        email: [
          {
            required: true,
            errorMessage: this.$t('settings.emailPlaceholder')
          }
        ]
      },
      maxDate: new Date().toISOString().split('T')[0], // 今天的日期
      genderOptions: [
        { value: 'unknown', text: this.$t('common.unknown') || '未知' },
        { value: 'male', text: this.$t('common.male') || '男' },
        { value: 'female', text: this.$t('common.female') || '女' }
      ],
      exerciseFrequencyOptions: [
        { value: 'occasionally', text: this.$t('common.occasionally') || '偶尔锻炼 (0-2次/周)' },
        { value: 'regular', text: this.$t('common.regular') || '定期锻炼 (3-5次/周)' },
        { value: 'athlete', text: this.$t('common.athlete') || '专业运动员 (6+次/周)' }
      ],
      fitnessGoalsOptions: [
        { value: 'lose_weight', text: this.$t('common.loseWeight') || '减重' },
        { value: 'gain_muscle', text: this.$t('common.gainMuscle') || '增肌' },
        { value: 'improve_endurance', text: this.$t('common.improveEndurance') || '提高耐力' },
        { value: 'increase_strength', text: this.$t('common.increaseStrength') || '增强力量' },
        { value: 'improve_flexibility', text: this.$t('common.improveFlexibility') || '提高柔韧性' },
        { value: 'maintain_health', text: this.$t('common.maintainHealth') || '保持健康' },
        { value: 'sports_performance', text: this.$t('common.sportsPerformance') || '运动表现' },
        { value: 'rehabilitation', text: this.$t('common.rehabilitation') || '康复训练' }
      ],
      obstaclesOptions: [
        { value: 'lack_time', text: this.$t('common.lackTime') || '缺乏时间' },
        { value: 'lack_motivation', text: this.$t('common.lackMotivation') || '缺乏动力' },
        { value: 'lack_knowledge', text: this.$t('common.lackKnowledge') || '缺乏知识' },
        { value: 'lack_equipment', text: this.$t('common.lackEquipment') || '缺乏设备' },
        { value: 'lack_space', text: this.$t('common.lackSpace') || '缺乏场地' },
        { value: 'physical_limitations', text: this.$t('common.physicalLimitations') || '身体限制' },
        { value: 'work_schedule', text: this.$t('common.workSchedule') || '工作安排' },
        { value: 'family_responsibilities', text: this.$t('common.familyResponsibilities') || '家庭责任' },
        { value: 'financial_constraints', text: this.$t('common.financialConstraints') || '经济限制' },
        { value: 'weather_conditions', text: this.$t('common.weatherConditions') || '天气条件' }
      ]
    }
  },
  computed: {
    userInfo() {
      return store.userInfo
    },
    bmi() {
      const height = parseFloat(this.formData.height)
      const weight = parseFloat(this.formData.weight)
      
      if (height && weight && height > 0) {
        const heightM = height / 100
        return (weight / (heightM * heightM)).toFixed(2)
      }
      return null
    },
    bmiStatus() {
      if (!this.bmi) return ''
      
      const bmiValue = parseFloat(this.bmi)
      if (bmiValue < 18.5) return this.$t('common.underweight')
      if (bmiValue < 24) return this.$t('common.normal')
      if (bmiValue < 28) return this.$t('common.overweight')
      return this.$t('common.obese')
    }
  },
  onLoad() {
    this.loadUserData()
  },
  methods: {
    async loadUserData() {
      try {
        uni.showLoading({ title: this.$t('settings.loading') })
        // 从当前用户信息加载数据
        if (this.userInfo) {
          this.formData = {
            name: this.userInfo.name || '',
            email: this.userInfo.email || '',
            gender: this.userInfo.gender || '',
            birthday: this.userInfo.birthday || '',
            height: this.userInfo.height ? this.userInfo.height.toString() : '',
            weight: this.userInfo.weight ? this.userInfo.weight.toString() : '',
            exercise_frequency: this.userInfo.exercise_frequency || '',
            fitness_goals: this.userInfo.fitness_goals || [],
            obstacles: this.userInfo.obstacles || []
          }
        }
        console.log('personal-details loaded userInfo:', this.userInfo)
        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('加载用户数据失败:', error)
        uni.showToast({
          title: this.$t('settings.loadFailed'),
          icon: 'error'
        })
      }
    },
    
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.form.validate()
        if (!valid) return
        
        uni.showLoading({ title: this.$t('settings.updating') })
        
        // 准备提交数据
        const submitData = {
          id: this.userInfo.id,
          name: this.formData.name,
          gender: this.formData.gender || null,
          birthday: this.formData.birthday || null,
          height: this.formData.height ? parseFloat(this.formData.height) : null,
          weight: this.formData.weight ? parseFloat(this.formData.weight) : null,
          exercise_frequency: this.formData.exercise_frequency || null,
          fitness_goals: this.formData.fitness_goals,
          obstacles: this.formData.obstacles
        }
        
        // 调用更新接口
        const response = await api.auth.updateProfile(submitData)
        
        if (response.success) {
          // 更新本地用户信息
          const updatedUserInfo = { ...this.userInfo, ...response.data.user }
          api.setUserInfo(updatedUserInfo)
          mutations.setUserInfo(updatedUserInfo)
          
          uni.hideLoading()
          uni.showToast({
            title: this.$t('settings.updateSuccess'),
            icon: 'success'
          })
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(response.message || this.$t('settings.updateFailed'))
        }
        
      } catch (error) {
        uni.hideLoading()
        console.error('更新失败:', error)
        uni.showToast({
          title: error.message || this.$t('settings.updateFailed'),
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.personal-details {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.bmi-display {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .bmi-value {
    font-size: 32rpx;
    font-weight: bold;
    color: #007AFF;
  }
  
  .bmi-status {
    font-size: 28rpx;
    color: #666;
    padding: 8rpx 16rpx;
    background-color: #f0f0f0;
    border-radius: 8rpx;
  }
}

.btn-group {
  margin-top: 40rpx;
  padding: 0 20rpx;
  
  .submit-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    color: white;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
  }
  
  .submit-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);
  }
}

// 表单样式优化
:deep(.uni-forms-item__content) {
  margin-top: 10rpx;
}

:deep(.uni-section__content) {
  margin-top: 20rpx;
}

:deep(.uni-easyinput__content-input) {
  height: 88rpx;
  font-size: 30rpx;
}

:deep(.uni-data-select) {
  height: 88rpx;
}

:deep(.uni-datetime-picker) {
  height: 88rpx;
}
</style>