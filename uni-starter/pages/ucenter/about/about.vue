<template>
	<view class="about">
		<view class="box">
			<image class="logoImg" :src="about.logo"></image>
			<text class="tip appName">{{about.appName}}</text>
			<text class="tip">Version {{version}}</text>
		</view>
      <view class="footer-agreement">
        <text class="agreement-text">
          <text class="agreement-link" @click="showTerms">Terms of Service</text>
          and
          <text class="agreement-link" @click="showPrivacy">Privacy Policy</text>
        </text>
      </view>
	</view>
</template>
<script>
	export default {
		components:{
		},
		onLoad() {
			// #ifdef APP-PLUS
			this.version = plus.runtime.version
			// #endif
		},
		computed: {
      /**
       * Show terms of service
       */
      showTerms() {
        uni.navigateTo({
          url: '/pages/legal/legal?type=agreement'
        })
      },

      /**
       * Show privacy policy
       */
      showPrivacy() {
        uni.navigateTo({
          url: '/pages/legal/legal?type=privacy'
        })
      },
			uniStarterConfig() {
				return getApp().globalData.config
			},
		},
		data() {
			return {
				version: "V1.0.0",
				year: "2020",
				about: {}
			};
		},
		created() {
			this.about = this.uniStarterConfig.about
			uni.setNavigationBarTitle({
				title: this.$t('about.about')+ " " + this.about.appName
			})
			this.year = (new Date).getFullYear()
		},
		onNavigationBarButtonTap() {},
		methods: {}
	}
</script>
<style lang="scss" scoped>
	/* #ifndef APP-NVUE */
	view{
		display: flex;
		box-sizing: border-box;
		flex-direction: column;
	}
	/* #endif */
	.about {
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.box {
		margin-top: 60px;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.logoImg {
		margin-bottom: 10rpx;
		width: 160rpx;
		height: 160rpx;
		border-radius: 15px;
	}

	.tip {
		text-align: center;
		font-size: 24rpx;
		margin-top: 10px;
		padding: 10rpx;
	}

	.appName {
		margin-top: 20px;
		font-size: 42rpx;
		font-weight: 500;
	}

	.qrcode ,.qrcode .uqrcode{
		margin: 10px 0;
		width: 100px;
		height: 100px;
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
	}

	.copyright {
		font-size: 32rpx;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		bottom: 20px;
		// left: 0;
		position: fixed;
	}

	.agreement-box {
		justify-content: center;
	}

	.agreement {
		color: #2285ff;
		font-size: 26rpx;
	}

	.hint {
		text-align: center;
		color: #999999;
		font-size: 26rpx;
	}
  .footer-agreement {
    padding: 30rpx 40rpx;

    .agreement-text {
      text-align: center;
      font-size: 24rpx;
      color: #999;
      line-height: 1.6;

      .agreement-link {
        color: #333;
        text-decoration: underline;
        font-weight: 500;
      }
    }
  }
</style>
