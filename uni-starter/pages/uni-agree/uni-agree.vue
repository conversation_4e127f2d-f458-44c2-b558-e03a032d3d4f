<template>
	<view class="agree-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<uni-icons type="back" size="24" color="#333"></uni-icons>
				</view>
				<view class="navbar-title">
					{{ currentTitle }}
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-container">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<uni-loading type="auto"></uni-loading>
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 错误状态 -->
			<view v-else-if="error" class="error-container">
				<uni-icons type="info" size="60" color="#f56c6c"></uni-icons>
				<text class="error-text">{{ errorMessage }}</text>
				<button class="retry-btn" @click="loadContent">重新加载</button>
			</view>
			
			<!-- 协议内容 -->
			<view v-else class="agreement-content">
				<view class="agreement-header">
					<text class="agreement-title">{{ contentData.title }}</text>
					<text v-if="contentData.updatedAt" class="agreement-date">
						更新时间：{{ formatDate(contentData.updatedAt) }}
					</text>
				</view>
				
				<view class="agreement-body">
					<rich-text :nodes="contentData.content || contentData.htmlContent"></rich-text>
				</view>
				
				<view v-if="contentData.version" class="agreement-footer">
					<text class="version-text">版本：{{ contentData.version }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'
	
	export default {
		data() {
			return {
				agreementType: '', // 'agreement' 或 'privacy'
				loading: true,
				error: false,
				errorMessage: '',
				contentData: {
					title: '',
					content: '',
					htmlContent: '',
					version: '',
					updatedAt: ''
				}
			}
		},
		computed: {
			currentTitle() {
				return this.agreementType === 'privacy' ? '隐私政策' : '用户协议'
			}
		},
		onLoad(options) {
			// 获取页面参数
			this.agreementType = options.type || 'agreement'
			// 设置页面标题
			uni.setNavigationBarTitle({
				title: this.currentTitle
			})
			// 加载内容
			this.loadContent()
		},
		methods: {
			/**
			 * 加载协议内容
			 */
			async loadContent() {
				this.loading = true
				this.error = false
				this.errorMessage = ''
				
				try {
					let response
					
					// 根据类型调用不同的API
					if (this.agreementType === 'privacy') {
						response = await api.legal.getPrivacyPolicy()
					} else {
						response = await api.legal.getUserAgreement()
					}
					
					if (response.success && response.data) {
						this.contentData = {
							title: response.data.title || this.currentTitle,
							content: response.data.content || '',
							htmlContent: response.data.html_content || response.data.htmlContent || '',
							version: response.data.version || '',
							updatedAt: response.data.updated_at || response.data.updatedAt || ''
						}
					} else {
						throw new Error(response.message || '获取内容失败')
					}
				} catch (error) {
					console.error('加载协议内容失败:', error)
					this.error = true
					this.errorMessage = error.message || '加载失败，请检查网络连接'
					
					// 如果API调用失败，使用本地默认内容
					this.loadDefaultContent()
				} finally {
					this.loading = false
				}
			},
			
			/**
			 * 加载默认内容（API失败时的后备方案）
			 */
			loadDefaultContent() {
				if (this.agreementType === 'privacy') {
					this.contentData = {
						title: '隐私政策',
						content: this.getDefaultPrivacyContent(),
						version: '1.0',
						updatedAt: new Date().toISOString()
					}
				} else {
					this.contentData = {
						title: '用户服务协议',
						content: this.getDefaultAgreementContent(),
						version: '1.0',
						updatedAt: new Date().toISOString()
					}
				}
				this.error = false
			},
			
			/**
			 * 获取默认用户协议内容
			 */
			getDefaultAgreementContent() {
				return `
					<h2>用户服务协议</h2>
					<h3>1. 服务条款的确认和接纳</h3>
					<p>您的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。</p>
					
					<h3>2. 服务简介</h3>
					<p>本服务是一个提供体育资讯、数据分析等功能的平台，我们为用户提供便捷的体育信息服务。</p>
					
					<h3>3. 用户账号</h3>
					<p>3.1 用户有义务保证密码和账号的安全。</p>
					<p>3.2 用户账号仅限本人使用，禁止赠与、借用、租用、转让或售卖。</p>
					
					<h3>4. 用户义务</h3>
					<p>4.1 用户在使用服务时，必须遵守相关法律法规。</p>
					<p>4.2 不得发布违法、违规、有害信息。</p>
					
					<h3>5. 隐私保护</h3>
					<p>我们非常重视用户隐私保护，具体内容请查看《隐私政策》。</p>
					
					<h3>6. 免责声明</h3>
					<p>在法律允许范围内，我们对服务中断、数据丢失等不承担责任。</p>
					
					<h3>7. 协议修改</h3>
					<p>我们有权根据需要修改本协议，修改后的协议将在平台公布。</p>
				`
			},
			
			/**
			 * 获取默认隐私政策内容
			 */
			getDefaultPrivacyContent() {
				return `
					<h2>隐私政策</h2>
					<h3>1. 信息收集</h3>
					<p>我们可能收集以下信息：</p>
					<p>1.1 您主动提供的信息：注册信息、个人资料等</p>
					<p>1.2 自动收集的信息：设备信息、使用记录等</p>
					
					<h3>2. 信息使用</h3>
					<p>我们收集信息的目的包括：</p>
					<p>2.1 提供、维护和改进我们的服务</p>
					<p>2.2 个性化用户体验</p>
					<p>2.3 保障平台安全</p>
					
					<h3>3. 信息分享</h3>
					<p>除以下情况外，我们不会向第三方分享您的个人信息：</p>
					<p>3.1 获得您的明确同意</p>
					<p>3.2 法律法规要求</p>
					<p>3.3 保护用户和公众的安全</p>
					
					<h3>4. 信息保护</h3>
					<p>我们采用业界标准的安全措施保护您的信息安全。</p>
					
					<h3>5. Cookie使用</h3>
					<p>我们可能使用Cookie来改善用户体验，您可以选择接受或拒绝Cookie。</p>
					
					<h3>6. 未成年人保护</h3>
					<p>我们特别重视未成年人的隐私保护，建议未成年人在监护人指导下使用我们的服务。</p>
					
					<h3>7. 联系我们</h3>
					<p>如果您对本隐私政策有任何疑问，请通过应用内反馈功能联系我们。</p>
				`
			},
			
			/**
			 * 格式化日期
			 */
			formatDate(dateString) {
				if (!dateString) return ''
				const date = new Date(dateString)
				return date.toLocaleDateString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit'
				})
			},
			
			/**
			 * 返回上一页
			 */
			goBack() {
				uni.navigateBack({
					fail: () => {
						// 如果无法返回上一页，跳转到首页
						uni.switchTab({
							url: '/pages/ucenter/ucenter'
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f9fa;
	}
	
	.agree-container {
		min-height: 100vh;
		background-color: #f8f9fa;
	}
	
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background-color: #fff;
		border-bottom: 1rpx solid #e4e7ed;
		/* #ifdef MP */
		padding-top: 44px; /* 小程序状态栏高度 */
		/* #endif */
		/* #ifdef H5 */
		padding-top: 0;
		/* #endif */
		
		.navbar-content {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			
			.navbar-left {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.navbar-title {
				flex: 1;
				text-align: center;
				font-size: 36rpx;
				font-weight: 600;
				color: #303133;
			}
			
			.navbar-right {
				width: 60rpx;
			}
		}
	}
	
	.content-container {
		/* #ifdef MP */
		margin-top: calc(44px + 88rpx); /* 小程序需要考虑状态栏高度 */
		/* #endif */
		/* #ifdef H5 */
		margin-top: 88rpx;
		/* #endif */
		padding: 40rpx 30rpx;
	}
	
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 0;
		
		.loading-text {
			margin-top: 30rpx;
			font-size: 28rpx;
			color: #909399;
		}
	}
	
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 0;
		
		.error-text {
			margin: 30rpx 0;
			font-size: 28rpx;
			color: #f56c6c;
			text-align: center;
		}
		
		.retry-btn {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: #fff;
			border: none;
			border-radius: 50rpx;
			padding: 20rpx 60rpx;
			font-size: 28rpx;
		}
	}
	
	.agreement-content {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}
	
	.agreement-header {
		margin-bottom: 40rpx;
		padding-bottom: 30rpx;
		border-bottom: 2rpx solid #f4f4f5;
		
		.agreement-title {
			display: block;
			font-size: 40rpx;
			font-weight: bold;
			color: #303133;
			margin-bottom: 20rpx;
		}
		
		.agreement-date {
			font-size: 24rpx;
			color: #909399;
		}
	}
	
	.agreement-body {
		line-height: 1.8;
		
		/* 富文本样式 */
		:deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
			color: #303133;
			margin: 30rpx 0 20rpx 0;
			font-weight: 600;
		}
		
		:deep(h2) {
			font-size: 36rpx;
		}
		
		:deep(h3) {
			font-size: 32rpx;
		}
		
		:deep(h4) {
			font-size: 30rpx;
		}
		
		:deep(p) {
			font-size: 28rpx;
			color: #606266;
			margin: 20rpx 0;
			text-indent: 2em;
		}
		
		:deep(ul), :deep(ol) {
			padding-left: 40rpx;
			margin: 20rpx 0;
		}
		
		:deep(li) {
			font-size: 28rpx;
			color: #606266;
			margin: 10rpx 0;
		}
		
		:deep(strong) {
			font-weight: 600;
			color: #303133;
		}
		
		:deep(em) {
			font-style: italic;
			color: #909399;
		}
	}
	
	.agreement-footer {
		margin-top: 60rpx;
		padding-top: 30rpx;
		border-top: 2rpx solid #f4f4f5;
		text-align: center;
		
		.version-text {
			font-size: 24rpx;
			color: #c0c4cc;
		}
	}
</style>