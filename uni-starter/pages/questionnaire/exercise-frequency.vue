<template>
	<view class="exercise-frequency-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">How often do you exercise?</text>
			<text class="subtitle">This helps us set realistic goals for you</text>
			
			<!-- Frequency Options -->
			<view class="frequency-options">
				<view 
					class="frequency-option" 
					:class="{ 'selected': selectedFrequency === 'occasionally' }"
					@click="selectFrequency('occasionally')"
				>
					<view class="option-icon">📅</view>
					<view class="option-content">
						<text class="option-title">0-2 times</text>
						<text class="option-subtitle">Occasionally exercise</text>
						<text class="option-description">Just getting started or exercising when you can</text>
					</view>
				</view>
				
				<view 
					class="frequency-option" 
					:class="{ 'selected': selectedFrequency === 'regular' }"
					@click="selectFrequency('regular')"
				>
					<view class="option-icon">💪</view>
					<view class="option-content">
						<text class="option-title">3-5 times</text>
						<text class="option-subtitle">Regular exercise routine</text>
						<text class="option-description">You have a consistent workout schedule</text>
					</view>
				</view>
				
				<view 
					class="frequency-option" 
					:class="{ 'selected': selectedFrequency === 'athlete' }"
					@click="selectFrequency('athlete')"
				>
					<view class="option-icon">🏆</view>
					<view class="option-content">
						<text class="option-title">6+ times</text>
						<text class="option-subtitle">Dedicated athlete</text>
						<text class="option-description">Training is a major part of your lifestyle</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': !selectedFrequency }"
				:disabled="!selectedFrequency"
				@click="continueToNext"
			>
				Continue
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedFrequency: ''
			}
		},
		methods: {
			selectFrequency(frequency) {
				this.selectedFrequency = frequency
			},
			
			continueToNext() {
				if (!this.selectedFrequency) return
				
				// Save exercise frequency
				uni.setStorageSync('questionnaire_exercise_frequency', this.selectedFrequency)
				
				// Go to next step
				uni.navigateTo({
					url: '/pages/questionnaire/obstacles'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			// Load saved data if exists
			const savedFrequency = uni.getStorageSync('questionnaire_exercise_frequency')
			if (savedFrequency) {
				this.selectedFrequency = savedFrequency
			}
		}
	}
</script>

<style lang="scss" scoped>
	.exercise-frequency-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;
		
		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}
		
		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;
			
			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		
		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}
		
		.subtitle {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 80rpx;
			line-height: 1.4;
		}
		
		.frequency-options {
			display: flex;
			flex-direction: column;
			gap: 30rpx;
			
			.frequency-option {
				display: flex;
				align-items: center;
				padding: 40rpx;
				border: 3rpx solid #e0e0e0;
				border-radius: 20rpx;
				background: #ffffff;
				transition: all 0.3s ease;
				
				&.selected {
					background: #333;
					border-color: #333;
					
					.option-content {
						.option-title,
						.option-subtitle,
						.option-description {
							color: #ffffff !important;
						}
					}
				}
				
				&:active {
					transform: scale(0.98);
				}
				
				.option-icon {
					margin-right: 30rpx;
					width: 80rpx;
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 48rpx;
				}
				
				.option-content {
					flex: 1;
					
					.option-title {
						font-size: 36rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 8rpx;
						display: block;
					}
					
					.option-subtitle {
						font-size: 28rpx;
						color: #666;
						font-weight: 600;
						margin-bottom: 8rpx;
						display: block;
					}
					
					.option-description {
						font-size: 24rpx;
						color: #999;
						line-height: 1.4;
						display: block;
					}
				}
			}
		}
	}
	
	.footer {
		padding: 60rpx 0 80rpx;
		
		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;
			
			&.disabled {
				background: #e0e0e0;
				color: #999;
			}
			
			&:not(.disabled):active {
				background: #555;
			}
		}
	}
</style>