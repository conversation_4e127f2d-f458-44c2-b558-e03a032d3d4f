<template>
	<view class="exercise-frequency-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">How often do you exercise?</text>
			<text class="subtitle">This helps us set realistic goals for you</text>
			
			<!-- Frequency Options -->
			<view class="frequency-options">
				<view 
					class="frequency-option" 
					:class="{ 'selected': selectedFrequency === 'occasionally' }"
					@click="selectFrequency('occasionally')"
				>
					<view class="option-icon">📅</view>
					<view class="option-content">
						<text class="option-title">0-2 times</text>
						<text class="option-subtitle">Occasionally exercise</text>
						<text class="option-description">Just getting started or exercising when you can</text>
					</view>
				</view>
				
				<view 
					class="frequency-option" 
					:class="{ 'selected': selectedFrequency === 'regular' }"
					@click="selectFrequency('regular')"
				>
					<view class="option-icon">💪</view>
					<view class="option-content">
						<text class="option-title">3-5 times</text>
						<text class="option-subtitle">Regular exercise routine</text>
						<text class="option-description">You have a consistent workout schedule</text>
					</view>
				</view>
				
				<view 
					class="frequency-option" 
					:class="{ 'selected': selectedFrequency === 'athlete' }"
					@click="selectFrequency('athlete')"
				>
					<view class="option-icon">🏆</view>
					<view class="option-content">
						<text class="option-title">6+ times</text>
						<text class="option-subtitle">Dedicated athlete</text>
						<text class="option-description">Training is a major part of your lifestyle</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': !selectedFrequency }"
				:disabled="!selectedFrequency"
				@click="continueToNext"
			>
				Continue
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedFrequency: ''
			}
		},
		methods: {
			selectFrequency(frequency) {
				this.selectedFrequency = frequency
			},
			
			continueToNext() {
				if (!this.selectedFrequency) return
				
				// Save exercise frequency
				uni.setStorageSync('questionnaire_exercise_frequency', this.selectedFrequency)
				
				// Go to next step
				uni.navigateTo({
					url: '/pages/questionnaire/obstacles'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			// Load saved data if exists
			const savedFrequency = uni.getStorageSync('questionnaire_exercise_frequency')
			if (savedFrequency) {
				this.selectedFrequency = savedFrequency
			}
		}
	}
</script>

<style lang="scss" scoped>
	.exercise-frequency-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;

		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			backdrop-filter: blur(10rpx);
			transition: all 0.3s ease;

			&:active {
				background: rgba(255, 255, 255, 0.2);
			}

			.back-icon {
				font-size: 40rpx;
				color: #ffffff;
				font-weight: bold;
			}
		}

		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;

			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
				border-radius: 4rpx;
				box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.2);
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;

		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #ffffff;
			margin-bottom: 30rpx;
			line-height: 1.2;
			letter-spacing: 1rpx;
		}

		.subtitle {
			font-size: 32rpx;
			color: #b0b0b0;
			margin-bottom: 80rpx;
			line-height: 1.4;
		}

		.frequency-options {
			display: flex;
			flex-direction: column;
			gap: 30rpx;

			.frequency-option {
				display: flex;
				align-items: center;
				padding: 40rpx;
				border: 2rpx solid rgba(255, 255, 255, 0.2);
				border-radius: 24rpx;
				background: rgba(255, 255, 255, 0.08);
				backdrop-filter: blur(10rpx);
				transition: all 0.3s ease;

				&.selected {
					background: rgba(255, 255, 255, 0.15);
					border-color: rgba(255, 255, 255, 0.6);
					box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.1);

					.option-content {
						.option-title,
						.option-subtitle,
						.option-description {
							color: #ffffff !important;
						}
					}
				}

				&:active {
					transform: scale(0.98);
				}
				
				.option-icon {
					margin-right: 30rpx;
					width: 80rpx;
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 48rpx;
				}

				.option-content {
					flex: 1;

					.option-title {
						font-size: 36rpx;
						font-weight: bold;
						color: #ffffff;
						margin-bottom: 8rpx;
						display: block;
					}

					.option-subtitle {
						font-size: 28rpx;
						color: #d0d0d0;
						font-weight: 600;
						margin-bottom: 8rpx;
						display: block;
					}

					.option-description {
						font-size: 24rpx;
						color: #b0b0b0;
						line-height: 1.4;
						display: block;
					}
				}
			}
		}
	}

	.footer {
		padding: 60rpx 0 80rpx;

		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #1a1a1a;
			transition: all 0.3s ease;
			box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.2);

			&.disabled {
				background: rgba(255, 255, 255, 0.2);
				color: #666666;
				box-shadow: none;
			}

			&:not(.disabled):active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.15);
			}
		}
	}
</style>