<template>
	<view class="obstacles-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">What prevents you from reaching your goals?</text>
			<text class="subtitle">Select all that apply - we'll help you overcome these challenges</text>
			
			<!-- Obstacles Options -->
			<view class="obstacles-options">
				<view 
					class="obstacle-option" 
					:class="{ 'selected': selectedObstacles.includes('lack_consistency') }"
					@click="toggleObstacle('lack_consistency')"
				>
					<view class="option-content">
						<text class="option-icon">🔄</text>
						<text class="option-text">Lack of consistency</text>
					</view>
					<view class="check-icon" v-if="selectedObstacles.includes('lack_consistency')">✓</view>
				</view>
				
				<view 
					class="obstacle-option" 
					:class="{ 'selected': selectedObstacles.includes('unhealthy_work_habits') }"
					@click="toggleObstacle('unhealthy_work_habits')"
				>
					<view class="option-content">
						<text class="option-icon">💼</text>
						<text class="option-text">Unhealthy work habits</text>
					</view>
					<view class="check-icon" v-if="selectedObstacles.includes('unhealthy_work_habits')">✓</view>
				</view>
				
				<view 
					class="obstacle-option" 
					:class="{ 'selected': selectedObstacles.includes('lack_support') }"
					@click="toggleObstacle('lack_support')"
				>
					<view class="option-content">
						<text class="option-icon">👥</text>
						<text class="option-text">Lack of support</text>
					</view>
					<view class="check-icon" v-if="selectedObstacles.includes('lack_support')">✓</view>
				</view>
				
				<view 
					class="obstacle-option" 
					:class="{ 'selected': selectedObstacles.includes('busy_schedule') }"
					@click="toggleObstacle('busy_schedule')"
				>
					<view class="option-content">
						<text class="option-icon">⏰</text>
						<text class="option-text">Busy schedule</text>
					</view>
					<view class="check-icon" v-if="selectedObstacles.includes('busy_schedule')">✓</view>
				</view>
				
				<view 
					class="obstacle-option" 
					:class="{ 'selected': selectedObstacles.includes('lack_motivation') }"
					@click="toggleObstacle('lack_motivation')"
				>
					<view class="option-content">
						<text class="option-icon">🔥</text>
						<text class="option-text">Lack of exercise inspiration</text>
					</view>
					<view class="check-icon" v-if="selectedObstacles.includes('lack_motivation')">✓</view>
				</view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': selectedObstacles.length === 0 }"
				:disabled="selectedObstacles.length === 0"
				@click="continueToNext"
			>
				Continue
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedObstacles: []
			}
		},
		methods: {
			toggleObstacle(obstacle) {
				const index = this.selectedObstacles.indexOf(obstacle)
				if (index > -1) {
					this.selectedObstacles.splice(index, 1)
				} else {
					this.selectedObstacles.push(obstacle)
				}
			},
			
			continueToNext() {
				if (this.selectedObstacles.length === 0) return
				
				// Save obstacles
				uni.setStorageSync('questionnaire_obstacles', this.selectedObstacles)
				
				// Go to final step
				uni.navigateTo({
					url: '/pages/questionnaire/personalization'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			// Load saved data if exists
			const savedObstacles = uni.getStorageSync('questionnaire_obstacles')
			if (savedObstacles && Array.isArray(savedObstacles)) {
				this.selectedObstacles = savedObstacles
			}
		}
	}
</script>

<style lang="scss" scoped>
	.obstacles-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;
		
		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}
		
		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;
			
			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		
		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}
		
		.subtitle {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 80rpx;
			line-height: 1.4;
		}
		
		.obstacles-options {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			
			.obstacle-option {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;
				border: 3rpx solid #e0e0e0;
				border-radius: 16rpx;
				background: #ffffff;
				transition: all 0.3s ease;
				
				&.selected {
					background: #333;
					border-color: #333;
					
					.option-content {
						.option-text {
							color: #ffffff !important;
						}
					}
				}
				
				&:active {
					transform: scale(0.98);
				}
				
				.option-content {
					display: flex;
					align-items: center;
					gap: 20rpx;
					
					.option-icon {
						font-size: 32rpx;
					}
					
					.option-text {
						font-size: 32rpx;
						color: #333;
						font-weight: 500;
					}
				}
				
				.check-icon {
					font-size: 24rpx;
					color: #ffffff;
					font-weight: bold;
				}
			}
		}
	}
	
	.footer {
		padding: 60rpx 0 80rpx;
		
		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;
			
			&.disabled {
				background: #e0e0e0;
				color: #999;
			}
			
			&:not(.disabled):active {
				background: #555;
			}
		}
	}
</style>