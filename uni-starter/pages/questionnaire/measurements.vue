<template>
	<view class="measurements-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">Height & Weight</text>
			<text class="subtitle">This will be taken into account when calculating your daily nutrition goals.</text>
			
			<!-- Unit Toggle -->
			<view class="unit-toggle">
				<text class="unit-label" :class="{ 'active': !isMetric }">Imperial</text>
				<view class="toggle-switch" @click="toggleUnit">
					<view class="toggle-slider" :class="{ 'metric': isMetric }"></view>
				</view>
				<text class="unit-label" :class="{ 'active': isMetric }">Metric</text>
			</view>
			
			<!-- Picker Container -->
			<view class="picker-container">
				<!-- Imperial Mode: 3 columns picker-view -->
				<picker-view 
					v-if="!isMetric"
					:key="'imperial'"
					class="picker-view" 
					:value="imperialPickerValue" 
					@change="onImperialPickerChange"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
				>
					<!-- Feet Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(feet, index) in feetRange" :key="index">
							{{ feet }} ft
						</view>
					</picker-view-column>
					
					<!-- Inches Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(inch, index) in inchesRange" :key="index">
							{{ inch }} in
						</view>
					</picker-view-column>
					
					<!-- Pounds Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(pound, index) in poundsRange" :key="index">
							{{ pound }} lb
						</view>
					</picker-view-column>
				</picker-view>
				
				<!-- Metric Mode: 2 columns picker-view -->
				<picker-view 
					v-else
					:key="'metric'"
					class="picker-view" 
					:value="metricPickerValue" 
					@change="onMetricPickerChange"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
				>
					<!-- Centimeters Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(cm, index) in centimetersRange" :key="index">
							{{ cm }} cm
						</view>
					</picker-view-column>
					
					<!-- Kilograms Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(kg, index) in kilogramsRange" :key="index">
							{{ kg }} kg
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': !isDataSelected }"
				:disabled="!isDataSelected"
				@click="continueToNext"
			>
				Continue
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isMetric: false, // Default to Imperial
				imperialPickerValue: [0, 0, 0], // [feetIndex, inchesIndex, poundsIndex]
				metricPickerValue: [0, 0], // [cmIndex, kgIndex]
				indicatorStyle: 'height: 100rpx; background: rgba(102, 126, 234, 0.1); border-top: 2rpx solid #667eea; border-bottom: 2rpx solid #667eea;',
				maskStyle: 'background: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(255,255,255,0.6), rgba(255,255,255,0.6), rgba(255,255,255,0.95));'
			}
		},
		computed: {
			// Feet range: 2 to 8
			feetRange() {
				const range = []
				for (let i = 2; i <= 8; i++) {
					range.push(i)
				}
				return range
			},
			
			// Inches range: 0 to 11
			inchesRange() {
				const range = []
				for (let i = 0; i <= 11; i++) {
					range.push(i)
				}
				return range
			},
			
			// Pounds range: 70 to 440
			poundsRange() {
				const range = []
				for (let i = 70; i <= 440; i++) {
					range.push(i)
				}
				return range
			},
			
			// Centimeters range: 120 to 220
			centimetersRange() {
				const range = []
				for (let i = 120; i <= 220; i++) {
					range.push(i)
				}
				return range
			},
			
			// Kilograms range: 30 to 200
			kilogramsRange() {
				const range = []
				for (let i = 30; i <= 200; i++) {
					range.push(i)
				}
				return range
			},
			
			// Check if data is selected
			isDataSelected() {
				if (this.isMetric) {
					return this.metricPickerValue[0] !== null && this.metricPickerValue[1] !== null
				} else {
					return this.imperialPickerValue[0] !== null && 
					       this.imperialPickerValue[1] !== null && 
					       this.imperialPickerValue[2] !== null
				}
			},
			
			// Get selected height in centimeters
			heightInCm() {
				if (this.isMetric) {
					return this.centimetersRange[this.metricPickerValue[0]]
				} else {
					const feet = this.feetRange[this.imperialPickerValue[0]]
					const inches = this.inchesRange[this.imperialPickerValue[1]]
					return Math.round((feet * 12 + inches) * 2.54)
				}
			},
			
			// Get selected weight in kilograms
			weightInKg() {
				if (this.isMetric) {
					return this.kilogramsRange[this.metricPickerValue[1]]
				} else {
					const pounds = this.poundsRange[this.imperialPickerValue[2]]
					return Math.round(pounds * 0.453592)
				}
			}
		},
		methods: {
			toggleUnit() {
				this.isMetric = !this.isMetric
				// Reset to default values when switching units
				this.resetToDefaults()
			},
			
			resetToDefaults() {
				if (this.isMetric) {
					// Default metric values: 170cm, 70kg
					const defaultCmIndex = this.centimetersRange.indexOf(170)
					const defaultKgIndex = this.kilogramsRange.indexOf(70)
					this.metricPickerValue = [
						defaultCmIndex >= 0 ? defaultCmIndex : 50,
						defaultKgIndex >= 0 ? defaultKgIndex : 40
					]
				} else {
					// Default imperial values: 5ft 6in, 154lb
					const defaultFeetIndex = this.feetRange.indexOf(5)
					const defaultInchesIndex = this.inchesRange.indexOf(6)
					const defaultPoundsIndex = this.poundsRange.indexOf(154)
					this.imperialPickerValue = [
						defaultFeetIndex >= 0 ? defaultFeetIndex : 3,
						defaultInchesIndex >= 0 ? defaultInchesIndex : 6,
						defaultPoundsIndex >= 0 ? defaultPoundsIndex : 84
					]
				}
			},
			
			onImperialPickerChange(e) {
				this.imperialPickerValue = e.detail.value
			},
			
			onMetricPickerChange(e) {
				this.metricPickerValue = e.detail.value
			},
			
			continueToNext() {
				if (!this.isDataSelected) return
				
				// Save measurements in standardized units
				uni.setStorageSync('questionnaire_height', this.heightInCm)
				uni.setStorageSync('questionnaire_weight', this.weightInKg)
				
				// Go to next step
				uni.navigateTo({
					url: '/pages/questionnaire/birthday'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			// Load saved data if exists
			const savedHeight = uni.getStorageSync('questionnaire_height')
			const savedWeight = uni.getStorageSync('questionnaire_weight')
			
			if (savedHeight && savedWeight) {
				// Set picker values based on saved data
				// For now, just set defaults and let user adjust
			}
			
			// Set default values
			this.resetToDefaults()
		}
	}
</script>

<style lang="scss" scoped>
	.measurements-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;
		
		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}
		
		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;
			
			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		
		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}
		
		.subtitle {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 80rpx;
			line-height: 1.4;
		}
		
		.unit-toggle {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 80rpx;
			
			.unit-label {
				font-size: 32rpx;
				color: #ccc;
				font-weight: 500;
				transition: color 0.3s ease;
				
				&.active {
					color: #333;
				}
			}
			
			.toggle-switch {
				width: 100rpx;
				height: 50rpx;
				background: #e0e0e0;
				border-radius: 25rpx;
				margin: 0 30rpx;
				position: relative;
				
				.toggle-slider {
					width: 40rpx;
					height: 40rpx;
					background: #fff;
					border-radius: 50%;
					position: absolute;
					top: 5rpx;
					left: 5rpx;
					transition: transform 0.3s ease;
					box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.2);
					
					&.metric {
						transform: translateX(50rpx);
					}
				}
			}
		}
		
		.picker-container {
			flex: 1;
			display: flex;
			align-items: center;
			
			.picker-view {
				width: 100%;
				height: 500rpx;
				
				.picker-column {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.picker-item {
						height: 100rpx;
						line-height: 100rpx;
						text-align: center;
						font-size: 36rpx;
						color: #666;
						font-weight: 500;
						transition: all 0.3s ease;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}
		}
	}
	
	.footer {
		padding: 60rpx 0 80rpx;
		
		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;
			
			&.disabled {
				background: #e0e0e0;
				color: #999;
			}
			
			&:not(.disabled):active {
				background: #555;
			}
		}
	}
</style>