<template>
	<view class="birthday-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">When were you born?</text>
			<text class="subtitle">This will be taken into account when calculating your daily nutrition goals.</text>
			
			<!-- Date Picker -->
			<view class="picker-container">
				<picker-view 
					class="picker-view" 
					:value="pickerValue" 
					@change="onPickerChange"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
				>
					<!-- Month Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(month, index) in months" :key="index">
							{{ month }}
						</view>
					</picker-view-column>
					
					<!-- Day Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(day, index) in days" :key="index">
							{{ String(day).padStart(2, '0') }}
						</view>
					</picker-view-column>
					
					<!-- Year Column -->
					<picker-view-column class="picker-column">
						<view class="picker-item" v-for="(year, index) in years" :key="index">
							{{ year }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': !isDateSelected }"
				:disabled="!isDateSelected"
				@click="continueToNext"
			>
				Continue
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedMonthIndex: 0,
				selectedDayIndex: 0,
				selectedYearIndex: 0,
				pickerValue: [0, 0, 0], // [monthIndex, dayIndex, yearIndex]
				indicatorStyle: 'height: 100rpx; background: rgba(102, 126, 234, 0.1); border-top: 2rpx solid #667eea; border-bottom: 2rpx solid #667eea;',
				maskStyle: 'background: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(255,255,255,0.6), rgba(255,255,255,0.6), rgba(255,255,255,0.95));'
			}
		},
		computed: {
			// Generate months array
			months() {
				return [
					'January', 'February', 'March', 'April', 'May', 'June',
					'July', 'August', 'September', 'October', 'November', 'December'
				]
			},
			
			// Generate days array based on selected month and year
			days() {
				const selectedMonth = this.selectedMonthIndex
				const selectedYear = this.years[this.selectedYearIndex]
				
				if (selectedYear && selectedMonth !== null) {
					// Calculate actual days in the selected month/year
					const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate()
					const days = []
					for (let day = 1; day <= daysInMonth; day++) {
						days.push(day)
					}
					return days
				}
				
				// Default to 31 days
				const days = []
				for (let day = 1; day <= 31; day++) {
					days.push(day)
				}
				return days
			},
			
			// Generate years array (100 years ago to 13 years ago)
			years() {
				const currentYear = new Date().getFullYear()
				const startYear = currentYear - 100
				const endYear = currentYear - 13
				const years = []
				for (let year = endYear; year >= startYear; year--) {
					years.push(year)
				}
				return years
			},
			
			// Check if a complete date is selected
			isDateSelected() {
				return this.selectedMonthIndex !== null && 
				       this.selectedDayIndex !== null && 
				       this.selectedYearIndex !== null
			},
			
			// Get selected values
			selectedMonth() {
				return this.months[this.selectedMonthIndex]
			},
			
			selectedDay() {
				return this.days[this.selectedDayIndex]
			},
			
			selectedYear() {
				return this.years[this.selectedYearIndex]
			},
			
			// Format selected date for display
			formattedDate() {
				if (!this.isDateSelected) return ''
				const date = new Date(this.selectedYear, this.selectedMonthIndex, this.selectedDay)
				return date.toLocaleDateString('en-US', { 
					year: 'numeric', 
					month: 'long', 
					day: 'numeric' 
				})
			},
			
			// Calculate age based on selected date
			age() {
				if (!this.isDateSelected) return null
				
				const today = new Date()
				const birthDate = new Date(this.selectedYear, this.selectedMonthIndex, this.selectedDay)
				
				let age = today.getFullYear() - birthDate.getFullYear()
				const monthDiff = today.getMonth() - birthDate.getMonth()
				
				if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
					age--
				}
				
				return age
			},
			
			// Generate date string for storage (YYYY-MM-DD format)
			dateString() {
				if (!this.isDateSelected) return ''
				const month = (this.selectedMonthIndex + 1).toString().padStart(2, '0')
				const day = this.selectedDay.toString().padStart(2, '0')
				return `${this.selectedYear}-${month}-${day}`
			}
		},
		methods: {
			onPickerChange(e) {
				const [monthIndex, dayIndex, yearIndex] = e.detail.value
				
				this.selectedMonthIndex = monthIndex
				this.selectedDayIndex = dayIndex
				this.selectedYearIndex = yearIndex
				
				this.pickerValue = [monthIndex, dayIndex, yearIndex]
				
				// Validate day selection when month/year changes
				this.validateDay()
			},
			
			validateDay() {
				// Check if current selected day is still valid for the new month/year
				if (this.selectedDayIndex >= this.days.length) {
					// Reset to last valid day
					this.selectedDayIndex = this.days.length - 1
					this.pickerValue[1] = this.selectedDayIndex
				}
			},
			
			continueToNext() {
				if (!this.isDateSelected) return
				
				// Save birthday in YYYY-MM-DD format
				uni.setStorageSync('questionnaire_birthday', this.dateString)
				
				// Go to next step
				uni.navigateTo({
					url: '/pages/questionnaire/exercise-frequency'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			// Set default values (30 years old, January 1st)
			const currentYear = new Date().getFullYear()
			const defaultYear = currentYear - 30
			const defaultMonthIndex = 0 // January
			const defaultDayIndex = 0 // 1st day
			
			// Load saved data if exists
			const savedBirthday = uni.getStorageSync('questionnaire_birthday')
			if (savedBirthday) {
				const date = new Date(savedBirthday)
				this.selectedMonthIndex = date.getMonth()
				this.selectedDayIndex = date.getDate() - 1 // Convert to 0-based index
				this.selectedYearIndex = this.years.indexOf(date.getFullYear())
			} else {
				// Set defaults
				this.selectedMonthIndex = defaultMonthIndex
				this.selectedDayIndex = defaultDayIndex
				this.selectedYearIndex = this.years.indexOf(defaultYear)
			}
			
			// Set picker values
			this.pickerValue = [this.selectedMonthIndex, this.selectedDayIndex, this.selectedYearIndex]
		}
	}
</script>

<style lang="scss" scoped>
	.birthday-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;
		
		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}
		
		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;
			
			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		
		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}
		
		.subtitle {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 100rpx;
			line-height: 1.4;
		}
		
		.picker-container {
			flex: 1;
			display: flex;
			align-items: center;
			
			.picker-view {
				width: 100%;
				height: 500rpx;
				
				.picker-column {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.picker-item {
						height: 100rpx;
						line-height: 100rpx;
						text-align: center;
						font-size: 36rpx;
						color: #666;
						font-weight: 500;
						transition: all 0.3s ease;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}
		}
	}
	
	.footer {
		padding: 60rpx 0 80rpx;
		
		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;
			
			&.disabled {
				background: #e0e0e0;
				color: #999;
			}
			
			&:not(.disabled):active {
				background: #555;
			}
		}
	}
</style>