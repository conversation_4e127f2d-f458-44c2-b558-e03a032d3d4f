<template>
	<view class="gender-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">What's your gender?</text>
			<text class="subtitle">Help us personalize your fitness journey</text>
			
			<!-- Gender Options -->
			<view class="gender-options">
				<view 
					class="gender-option" 
					:class="{ 'selected': selectedGender === 'male' }"
					@click="selectGender('male')"
				>
					<text class="option-icon">♂</text>
					<text class="option-text">Male</text>
				</view>
				
				<view 
					class="gender-option" 
					:class="{ 'selected': selectedGender === 'female' }"
					@click="selectGender('female')"
				>
					<text class="option-icon">♀</text>
					<text class="option-text">Female</text>
				</view>
				
				<view 
					class="gender-option" 
					:class="{ 'selected': selectedGender === 'unknown' }"
					@click="selectGender('unknown')"
				>
					<text class="option-icon">⚬</text>
					<text class="option-text">Prefer not to say</text>
				</view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': !selectedGender }"
				:disabled="!selectedGender"
				@click="continueToNext"
			>
				Continue
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedGender: ''
			}
		},
		methods: {
			selectGender(gender) {
				this.selectedGender = gender
				console.log('Selected gender:', gender) // 添加调试日志
			},
			
			continueToNext() {
				console.log('Continue clicked, selected gender:', this.selectedGender) // 添加调试日志
				
				if (!this.selectedGender) {
					console.log('No gender selected') // 添加调试日志
					return
				}
				
				// Save to storage for later submission
				uni.setStorageSync('questionnaire_gender', this.selectedGender)
				console.log('Gender saved to storage') // 添加调试日志
				
				// Go to next step
				uni.navigateTo({
					url: '/pages/questionnaire/measurements',
					success: () => {
						console.log('Navigation to measurements successful') // 添加调试日志
					},
					fail: (err) => {
						console.error('Navigation failed:', err) // 添加调试日志
					}
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			console.log('Gender page loaded') // 添加调试日志
			// Load saved data if exists
			const savedGender = uni.getStorageSync('questionnaire_gender')
			if (savedGender) {
				this.selectedGender = savedGender
				console.log('Loaded saved gender:', savedGender) // 添加调试日志
			}
		}
	}
</script>

<style lang="scss" scoped>
	.gender-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;
		
		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			backdrop-filter: blur(10rpx);
			transition: all 0.3s ease;

			&:active {
				background: rgba(255, 255, 255, 0.2);
			}

			.back-icon {
				font-size: 40rpx;
				color: #ffffff;
				font-weight: bold;
			}
		}
		
		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;
			
			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		
		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}
		
		.subtitle {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 100rpx;
			line-height: 1.4;
		}
		
		.gender-options {
			display: flex;
			flex-direction: column;
			gap: 30rpx;
			
			.gender-option {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 160rpx;
				border: 3rpx solid #e0e0e0;
				border-radius: 20rpx;
				background: #ffffff;
				transition: all 0.3s ease;
				
				&.selected {
					background: #333;
					border-color: #333;
					
					.option-text, .option-icon {
						color: #ffffff !important;
					}
				}
				
				&:active {
					transform: scale(0.98);
				}
				
				.option-icon {
					font-size: 48rpx;
					color: #333;
					margin-bottom: 16rpx;
					font-weight: bold;
				}
				
				.option-text {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
				}
			}
		}
	}
	
	.footer {
		padding: 60rpx 0 80rpx;
		
		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;
			
			&.disabled {
				background: #e0e0e0;
				color: #999;
			}
			
			&:not(.disabled):active {
				background: #555;
			}
		}
	}
</style>