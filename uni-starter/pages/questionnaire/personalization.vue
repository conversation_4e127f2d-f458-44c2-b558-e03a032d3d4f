<template>
	<view class="personalization-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>
		
		<!-- Content -->
		<view class="content">
			<text class="title">Thank you for your trust</text>
			<text class="subtitle">Now let's personalize Motion for you</text>
			
			<!-- Information Cards -->
			<view class="info-section">
				<view class="info-card">
					<view class="info-icon">💖</view>
					<view class="info-content">
						<text class="info-title">Your health and safety matter to us</text>
						<text class="info-description">We're committed to protecting your personal information and privacy</text>
					</view>
				</view>
				
				<view class="info-card">
					<view class="info-icon">🔒</view>
					<view class="info-content">
						<text class="info-title">Data security guaranteed</text>
						<text class="info-description">Your information is encrypted and stored securely</text>
					</view>
				</view>
				
				<view class="info-card">
					<view class="info-icon">⭐</view>
					<view class="info-content">
						<text class="info-title">Personalized experience</text>
						<text class="info-description">Get recommendations tailored specifically for you</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn"
				@click="continueToNext"
			>
				Continue to Registration
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			continueToNext() {
				// Go to email registration
				uni.navigateTo({
					url: '/pages/login/email'
				})
			},
			
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.personalization-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;
		
		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}
		
		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;
			
			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}
	
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		
		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}
		
		.subtitle {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 80rpx;
			line-height: 1.4;
		}
		
		.info-section {
			display: flex;
			flex-direction: column;
			gap: 30rpx;
			
			.info-card {
				display: flex;
				align-items: flex-start;
				padding: 40rpx;
				background: #f8f9fa;
				border-radius: 20rpx;
				border: 2rpx solid #e9ecef;
				
				.info-icon {
					margin-right: 30rpx;
					margin-top: 10rpx;
					font-size: 40rpx;
				}
				
				.info-content {
					flex: 1;
					
					.info-title {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 10rpx;
						display: block;
					}
					
					.info-description {
						font-size: 28rpx;
						color: #666;
						line-height: 1.5;
						display: block;
					}
				}
			}
		}
	}
	
	.footer {
		padding: 60rpx 0 80rpx;
		
		.continue-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;
			
			&:active {
				background: #555;
			}
		}
	}
</style>