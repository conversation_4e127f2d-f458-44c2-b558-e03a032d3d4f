<template>
	<view class="legal-container">
		<!-- Header -->
		<view class="legal-header">
			<text class="title">{{ documentTitle }}</text>
		</view>
		
		<!-- Content Area -->
		<view class="legal-content">
			<view v-if="loading" class="loading-container">
				<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
			</view>
			
			<view v-else-if="error" class="error-container">
				<text class="error-text">{{ error }}</text>
				<button class="retry-btn" @click="loadDocument">Reload</button>
			</view>
			
			<view v-else class="document-content">
				<text class="content-text">{{ documentContent }}</text>
			</view>
		</view>
		
		<!-- Bottom Button -->
		<view class="legal-footer">
			<button class="back-btn" @click="goBack">Back</button>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'

	export default {
		data() {
			return {
				documentType: '',
				documentContent: '',
				loading: false,
				error: '',
				loadingText: {
					contentdown: 'Pull up to load more',
					contentrefresh: 'Loading...',
					contentnomore: 'No more data'
				}
			}
		},
		computed: {
			documentTitle() {
				return this.documentType === 'agreement' ? 'Terms of Service' : 'Privacy Policy'
			}
		},
		methods: {
			/**
			 * Load legal document
			 */
			async loadDocument() {
				if (!this.documentType) {
					this.error = 'Document type error'
					return
				}
				
				this.loading = true
				this.error = ''
				
				try {
					let response
					if (this.documentType === 'agreement') {
						response = await api.legal.getTermsOfService()
					} else if (this.documentType === 'privacy') {
						response = await api.legal.getPrivacyPolicy()
					} else {
						this.error = 'Unsupported document type'
						return
					}
					
					if (response.success && response.data) {
						this.documentContent = response.data.content || 'No content available'
					} else {
						this.error = response.message || 'Failed to get document'
					}
				} catch (error) {
					console.error('Failed to get legal document:', error)
					this.error = 'Network error, please try again later'
				} finally {
					this.loading = false
				}
			},
			
			/**
			 * Go back to previous page
			 */
			goBack() {
				uni.navigateBack()
			}
		},
		onLoad(options) {
			// Get document type parameter
			this.documentType = options.type || 'agreement'
			
			// Set navigation bar title
			uni.setNavigationBarTitle({
				title: this.documentTitle
			})
			
			// Load document content
			this.loadDocument()
		}
	}
</script>

<style lang="scss" scoped>
	.legal-container {
		min-height: 100vh;
		background: #f8f9fa;
		display: flex;
		flex-direction: column;
	}
	
	.legal-header {
		background: #fff;
		padding: 40rpx;
		border-bottom: 1rpx solid #e9ecef;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			text-align: center;
		}
	}
	
	.legal-content {
		flex: 1;
		padding: 40rpx;
		
		.loading-container {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 200rpx;
		}
		
		.error-container {
			text-align: center;
			padding: 80rpx 40rpx;
			
			.error-text {
				font-size: 32rpx;
				color: #dc3545;
				margin-bottom: 40rpx;
			}
			
			.retry-btn {
				background: #007bff;
				color: #fff;
				border: none;
				border-radius: 10rpx;
				padding: 20rpx 40rpx;
				font-size: 28rpx;
			}
		}
		
		.document-content {
			background: #fff;
			border-radius: 20rpx;
			padding: 40rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			
			.content-text {
				font-size: 30rpx;
				line-height: 1.8;
				color: #333;
				text-align: justify;
				white-space: pre-wrap;
			}
		}
	}
	
	.legal-footer {
		padding: 40rpx;
		background: #fff;
		border-top: 1rpx solid #e9ecef;
		
		.back-btn {
			width: 100%;
			height: 80rpx;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 40rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
		}
	}
</style>