<template>
	<view class="welcome-container">
		<!-- Status Bar Spacer -->
		<view class="status-bar-spacer"></view>
		
		<!-- App Introduction -->
		<view class="app-intro">
			<view class="logo-wrapper">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			</view>
			<text class="app-name">Motion</text>
			<text class="app-description">
				Transform your fitness journey with personalized AI coaching and comprehensive health tracking
			</text>
			
			<!-- Feature highlights -->
			<view class="features">
				<view class="feature-item">
					<text class="feature-icon">💖</text>
					<text class="feature-text">Personalized Fitness Plans</text>
				</view>
				<view class="feature-item">
					<text class="feature-icon">🤖</text>
					<text class="feature-text">AI-Powered Coaching</text>
				</view>
				<view class="feature-item">
					<text class="feature-icon">📊</text>
					<text class="feature-text">Comprehensive Tracking</text>
				</view>
			</view>
		</view>
		
		<!-- Action Buttons -->
		<view class="action-section">
			<!-- Get Started Button (Questionnaire) -->
			<button 
				class="get-started-btn" 
				@click="startQuestionnaire"
			>
				Get Started
			</button>
			
			<!-- Login Link -->
			<view class="login-section">
				<button 
					class="login-btn" 
					@click="goToLogin"
				>
					Login
				</button>
			</view>
		</view>
		
		<!-- Footer Agreement -->
		<view class="footer-agreement">
			<text class="agreement-text">
				By continuing, you agree to our
				<text class="agreement-link" @click="showTerms">Terms of Service</text>
				and
				<text class="agreement-link" @click="showPrivacy">Privacy Policy</text>
			</text>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'

	export default {
		data() {
			return {
				
			}
		},
		methods: {
			/**
			 * Start questionnaire flow
			 */
			startQuestionnaire() {
				uni.navigateTo({
					url: '/pages/questionnaire/gender'
				})
			},
			
			/**
			 * Go to login page
			 */
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/email'
				})
			},

			
			/**
			 * Show terms of service
			 */
			showTerms() {
				uni.navigateTo({
					url: '/pages/legal/legal?type=agreement'
				})
			},
			
			/**
			 * Show privacy policy
			 */
			showPrivacy() {
				uni.navigateTo({
					url: '/pages/legal/legal?type=privacy'
				})
			}
		},
		onLoad() {
			// Always show welcome page to let user choose between questionnaire and login
			// The actual auth check will happen when they make a choice
		},
    onShow() {
			// Check if user is already logged in
			api.users.getCurrentUser().then(res => {
				if (res.data) {
					// User is already logged in, navigate to home page
					uni.navigateTo({
						url: '/pages/ucenter/ucenter'
					})
				}
			})
		}
	}
</script>

<style lang="scss" scoped>
	.welcome-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		position: relative;
	}
	
	.status-bar-spacer {
		height: var(--status-bar-height, 44px);
	}
	
	.app-intro {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		text-align: center;

		.logo-wrapper {
			margin-bottom: 40rpx;

			.logo {
				width: 160rpx;
				height: 160rpx;
				border-radius: 32rpx;
				box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
			}
		}
		
		.app-name {
			font-size: 72rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.app-description {
			font-size: 32rpx;
			color: #666;
			line-height: 1.5;
			margin-bottom: 60rpx;
			max-width: 600rpx;
		}
		
		.features {
			display: flex;
			flex-direction: column;
			gap: 30rpx;
			
			.feature-item {
				display: flex;
				align-items: center;
				gap: 20rpx;
				justify-content: center;
				
				.feature-icon {
					font-size: 32rpx;
				}
				
				.feature-text {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}
	}
	
	.action-section {
		padding: 40rpx;
		
		.get-started-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			margin-bottom: 30rpx;
			transition: all 0.3s ease;
			
			&:active {
				background: #555;
			}
		}
		
		.login-section {
			display: flex;
			justify-content: center;
			margin-bottom: 20rpx;
			
			.login-btn {
				width: 100%;
				height: 120rpx;
				background: #ffffff;
				border: 3rpx solid #333;
				border-radius: 60rpx;
				font-size: 36rpx;
				font-weight: 600;
				color: #333;
				transition: all 0.3s ease;
				
				&:active {
					background: #f5f5f5;
				}
			}
		}
		
		.debug-section {
			display: flex;
			justify-content: center;
			
			.debug-btn {
				width: 100%;
				height: 80rpx;
				background: #f5f5f5;
				border: 2rpx solid #e0e0e0;
				border-radius: 40rpx;
				font-size: 28rpx;
				color: #666;
				transition: all 0.3s ease;
				
				&:active {
					background: #e8e8e8;
				}
			}
		}
	}
	
	.footer-agreement {
		padding: 30rpx 40rpx;
		
		.agreement-text {
			text-align: center;
			font-size: 24rpx;
			color: #999;
			line-height: 1.6;
			
			.agreement-link {
				color: #333;
				text-decoration: underline;
				font-weight: 500;
			}
		}
	}
</style>