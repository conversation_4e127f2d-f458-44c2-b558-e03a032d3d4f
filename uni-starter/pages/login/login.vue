<template>
	<view class="login-container">
		<!-- 顶部区域 -->
		<view class="login-header">
			<view class="logo-wrapper">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			</view>
			<text class="app-name">Motion</text>
		</view>
		
		<!-- 登录表单 -->
		<view class="login-form">
			<!-- 邮箱输入 -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.email" 
					:placeholder="$t('common.emailPlaceholder')"
					type="email"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="email" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- 密码输入 -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.password" 
					:placeholder="$t('pwdLogin.passwordPlaceholder')"
					type="password"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="locked" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- 登录按钮 -->
			<button 
				class="login-btn" 
				:class="{ 'login-btn-disabled': !canLogin }"
				:disabled="!canLogin"
				@click="handleLogin"
			>
				{{ $t('common.login') }}
			</button>
			
			<!-- Feature Links -->
			<view class="login-links">
				<text class="link-text" @click="goToRegister">
					{{ $t('pwdLogin.register') }}
				</text>
				<text class="link-text" @click="goToForgotPassword">
					{{ $t('pwdLogin.forgetPassword') }}
				</text>
			</view>
		</view>
		
		<!-- Bottom Agreement -->
		<view class="login-footer">
			<text class="agreement-text">
				By logging in, you agree to the
				<text class="agreement-link" @click="showAgreement">
					Terms of Service
				</text>
				and
				<text class="agreement-link" @click="showPrivacy">
					Privacy Policy
				</text>
			</text>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'
	import {
		store,
		mutations
	} from '@/common/store.js'

	export default {
		data() {
			return {
				formData: {
					email: '',
					password: ''
				},
				isLoading: false
			}
		},
		computed: {
			appConfig() {
				return getApp().globalData.config
			},
			canLogin() {
				return this.formData.email.trim() && 
					   this.formData.password.trim() && 
					   !this.isLoading
			}
		},
		methods: {
			/**
			 * 处理登录
			 */
			async handleLogin() {
				if (!this.canLogin) return
				
				// Simple email format validation
				if (!this.validateEmail(this.formData.email)) {
					api.showError('Please enter a valid email address')
					return
				}
				
				this.isLoading = true
				
				try {
					// 调用登录API
					const response = await api.auth.login({
						email: this.formData.email.trim(),
						password: this.formData.password.trim()
					})
					
					// Login successful
					if (response.success && response.data.token) {
						// Save token and user info
						api.setToken(response.data.token)
						api.setUserInfo(response.data.user)
						
						// Update user info in store
						mutations.loginSuccess(response.data.user)
						
						// Show success message
						api.showSuccess(response.message || 'Login successful')
						
						// Delayed redirect
						setTimeout(() => {
							this.goBack()
						}, 1000)
					}
				} catch (error) {
					console.error('Login failed:', error)
					// Error handling is done in api.js
				} finally {
					this.isLoading = false
				}
			},
			
			/**
			 * Validate email format
			 */
			validateEmail(email) {
				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
				return emailRegex.test(email)
			},
			
			/**
			 * Go back to previous page
			 */
			goBack() {
				const pages = getCurrentPages()
				if (pages.length > 1) {
					uni.navigateBack()
				} else {
					// If accessing login page directly, go to home
					uni.switchTab({
						url: '/pages/ucenter/ucenter'
					})
				}
			},
			
			/**
			 * Go to register page
			 */
			goToRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			
			/**
			 * Go to forgot password page
			 */
			goToForgotPassword() {
				uni.navigateTo({
					url: '/pages/forgot-password/forgot-password'
				})
			},
			
			/**
			 * Show user agreement
			 */
			showAgreement() {
				uni.navigateTo({
					url: '/pages/legal/legal?type=agreement'
				})
			},
			
			/**
			 * Show privacy policy
			 */
			showPrivacy() {
				uni.navigateTo({
					url: '/pages/legal/legal?type=privacy'
				})
			}
		},
		onLoad() {
			// Check if already logged in
			const token = api.getToken()
			if (token) {
				// Already logged in, go back
				this.goBack()
			}
		},
		onNavigationBarButtonTap(e) {
			if (e.index === 0) {
				this.goBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 0 40rpx;
		display: flex;
		flex-direction: column;
	}
	
	.login-header {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;

    .logo-wrapper {
      margin-bottom: 40rpx;

      .logo {
        width: 120rpx;
        height: 120rpx;
        border-radius: 20rpx;
        box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
      }
    }
		
		.app-name {
			font-size: 48rpx;
			font-weight: bold;
			color: #ffffff;
			margin-bottom: 20rpx;
		}
		
		.app-slogan {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}
	
	.login-form {
		margin: 60rpx 0;
		
		.input-group {
			background: rgba(255, 255, 255, 0.9);
			border-radius: 12rpx;
			margin-bottom: 30rpx;
			padding: 0 30rpx;
			
			:deep(.uni-easyinput__content) {
				background: transparent !important;
				border: none !important;
			}
			
			:deep(.uni-easyinput__content-input) {
				height: 100rpx;
				line-height: 100rpx;
				font-size: 32rpx;
				color: #333;
			}
		}
		
		.login-btn {
			width: 100%;
			height: 100rpx;
			background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
			border: none;
			border-radius: 12rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #ffffff;
			margin-top: 40rpx;
			box-shadow: 0 8rpx 24rpx rgba(238, 90, 36, 0.3);
		}
		
		.login-btn-disabled {
			background: #ccc !important;
			box-shadow: none !important;
		}
		
		.login-links {
			display: flex;
			justify-content: space-between;
			margin-top: 40rpx;
			
			.link-text {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.9);
				text-decoration: underline;
			}
		}
	}
	
	.login-footer {
		padding-bottom: 60rpx;
		
		.agreement-text {
			text-align: center;
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.7);
			line-height: 1.6;
			
			.agreement-link {
				color: rgba(255, 255, 255, 0.9);
				text-decoration: underline;
			}
		}
	}
</style>