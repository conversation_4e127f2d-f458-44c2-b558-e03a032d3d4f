<template>
	<view class="email-login-container">
		<!-- Header -->
		<view class="header">
			<uni-icons type="back" size="28" color="#333" @click="goBack"></uni-icons>
		</view>
		
		<!-- Content -->
		<view class="content">
			<view class="logo-section">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<text class="app-name">Motion</text>
			</view>
			
			<text class="title">Welcome back</text>
			<text class="subtitle">Enter your email to sign in</text>
			
			<!-- Email Input -->
			<view class="input-section">
				<view class="input-group">
					<uni-easyinput 
						v-model="email" 
						placeholder="Enter your email address"
						type="email"
						trim="both"
						clearable
						:inputBorder="false"
					>
						<template v-slot:left>
							<uni-icons type="email" size="22" color="#999"></uni-icons>
						</template>
					</uni-easyinput>
				</view>
			</view>
		</view>
		
		<!-- Continue Button -->
		<view class="footer">
			<button 
				class="continue-btn" 
				:class="{ 'disabled': !canContinue || isLoading }"
				:disabled="!canContinue || isLoading"
				@click="sendVerificationCode"
			>
				{{ isLoading ? 'Sending...' : 'Continue' }}
			</button>
			
			<!-- Sign up link -->
			<view class="signup-section">
				<text class="signup-text">Don't have an account?</text>
				<text class="signup-link" @click="goToWelcome">Get Started</text>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'

	export default {
		data() {
			return {
				email: '',
				isLoading: false
			}
		},
		computed: {
			canContinue() {
				return this.email.trim() && this.validateEmail(this.email)
			}
		},
		methods: {
			validateEmail(email) {
				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
				return emailRegex.test(email)
			},
			
			async sendVerificationCode() {
				if (!this.canContinue) return
				
				this.isLoading = true
				
				try {
					// Send verification code for login
					await api.verificationCode.send({
						email: this.email.trim(),
						purpose: 'email_verification'
					})
					
					// Save email for next page
					uni.setStorageSync('login_email', this.email.trim())
					
					// Go to verification page
					uni.navigateTo({
						url: '/pages/login/verification'
					})
					
				} catch (error) {
					console.error('Send verification code failed:', error)
					// Error is handled in api.js
				} finally {
					this.isLoading = false
				}
			},
			
			goBack() {
				uni.navigateBack()
			},
			
			goToWelcome() {
				uni.navigateTo({
					url: '/pages/welcome/welcome'
				})
			}
		},
		onLoad() {
			// Load saved email if exists
			const savedEmail = uni.getStorageSync('login_email')
			if (savedEmail) {
				this.email = savedEmail
			}
		}
	}
</script>

<style lang="scss" scoped>
	.email-login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
		display: flex;
		flex-direction: column;
	}

	.header {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 60rpx 40rpx 40rpx;
	}

	.content {
		flex: 1;
		padding: 0 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.logo-section {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 60rpx;

			.logo {
				width: 120rpx;
				height: 120rpx;
				border-radius: 24rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.1);
			}

			.app-name {
				font-size: 36rpx;
				font-weight: bold;
				color: #ffffff;
				letter-spacing: 2rpx;
			}
		}

		.title {
			font-size: 48rpx;
			font-weight: bold;
			color: #ffffff;
			text-align: center;
			margin-bottom: 20rpx;
			letter-spacing: 1rpx;
		}

		.subtitle {
			font-size: 28rpx;
			color: #b0b0b0;
			text-align: center;
			margin-bottom: 80rpx;
			line-height: 1.5;
		}

		.input-section {
			width: 100%;

			.input-group {
				background: rgba(255, 255, 255, 0.08);
				border-radius: 16rpx;
				padding: 0 30rpx;
				border: 2rpx solid rgba(255, 255, 255, 0.15);
				backdrop-filter: blur(10rpx);
				transition: all 0.3s ease;

				&:focus-within {
					border-color: rgba(255, 255, 255, 0.3);
					background: rgba(255, 255, 255, 0.12);
				}

				:deep(.uni-easyinput__content) {
					background: transparent !important;
					border: none !important;
				}

				:deep(.uni-easyinput__content-input) {
					height: 100rpx;
					line-height: 100rpx;
					font-size: 32rpx;
					color: #ffffff;

					&::placeholder {
						color: #888888;
					}
				}
			}
		}
	}

	.footer {
		padding: 40rpx;

		.continue-btn {
			width: 100%;
			height: 100rpx;
			background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
			border: none;
			border-radius: 50rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #1a1a1a;
			margin-bottom: 40rpx;
			box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.2);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.15);
			}

			&.disabled {
				background: rgba(255, 255, 255, 0.2) !important;
				color: #666666 !important;
				box-shadow: none !important;
			}
		}

		.signup-section {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;

			.signup-text {
				font-size: 28rpx;
				color: #b0b0b0;
			}

			.signup-link {
				font-size: 28rpx;
				color: #ffffff;
				font-weight: bold;
				text-decoration: underline;
			}
		}
	}
</style>