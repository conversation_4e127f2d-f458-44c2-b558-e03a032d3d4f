<template>
	<view class="login-verification-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="step-indicator">
				<view class="step-bar"></view>
			</view>
		</view>

		<!-- Content -->
		<view class="content">
			<text class="title">Enter verification code</text>
			<text class="subtitle">
				We've sent a 6-digit code to
				<text class="email-text">{{ maskedEmail }}</text>
			</text>

			<!-- Code Input -->
			<view class="code-section">
				<!-- Hidden input for continuous typing -->
				<input ref="hiddenInput" v-model="fullCode" type="number" maxlength="6" class="hidden-input"
					@input="onCodeInput" @focus="inputFocused = true" @blur="inputFocused = false" :focus="shouldFocus"
					:cursor-spacing="0" />

				<!-- Visual code display -->
				<view class="code-inputs" @click="focusHiddenInput">
					<view v-for="(digit, index) in displayDigits" :key="index" class="code-input" :class="{
							'active': inputFocused && index === fullCode.length, 
							'filled': digit !== '',
							'cursor': inputFocused && index === fullCode.length
						}" @click="focusHiddenInput">
						<text class="digit-text">{{ digit }}</text>
						<view v-if="inputFocused && index === fullCode.length" class="cursor-line"></view>
					</view>
				</view>
			</view>

			<!-- Resend Section -->
			<view class="resend-section">
				<text class="resend-text">Didn't receive the code?</text>
				<text class="resend-link" :class="{ 'disabled': countdown > 0 || isResending }" @click="resendCode">
					{{ countdown > 0 ? `Resend in ${countdown}s` : (isResending ? 'Sending...' : 'Resend') }}
				</text>
			</view>
		</view>

		<!-- Sign In Button -->
		<view class="footer">
			<button class="signin-btn" :class="{ 'disabled': !isCodeComplete || isLoading }"
				:disabled="!isCodeComplete || isLoading" @click="signIn">
				{{ isLoading ? 'Signing In...' : 'Sign In' }}
			</button>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'
	import {
		store,
		mutations
	} from '@/common/store.js'

	export default {
		data() {
			return {
				fullCode: '',
				inputFocused: false,
				shouldFocus: true,
				countdown: 0,
				isResending: false,
				isLoading: false,
				email: ''
			}
		},
		computed: {
			displayDigits() {
				const digits = []
				for (let i = 0; i < 6; i++) {
					digits.push(this.fullCode[i] || '')
				}
				return digits
			},
			isCodeComplete() {
				return this.fullCode.length === 6
			},
			verificationCode() {
				return this.fullCode
			},
			maskedEmail() {
				if (!this.email) return ''
				const [username, domain] = this.email.split('@')
				const maskedUsername = username.length > 2 ?
					username.substring(0, 2) + '*'.repeat(username.length - 2) :
					username
				return `${maskedUsername}@${domain}`
			}
		},
		methods: {
			onCodeInput(event) {
				let value = event.detail.value.replace(/\D/g, '') // Remove non-digits
				if (value.length > 6) {
					value = value.slice(0, 6)
				}
				this.fullCode = value

				// Auto submit when complete
				if (value.length === 6) {
					setTimeout(() => {
						this.signIn()
					}, 200) // Small delay for better UX
				}
			},

			focusHiddenInput() {
				this.shouldFocus = true
				this.inputFocused = true
				this.$nextTick(() => {
					// In UniApp, we need to trigger focus manually
					setTimeout(() => {
						this.shouldFocus = false
						this.$nextTick(() => {
							this.shouldFocus = true
						})
					}, 50)
				})
			},

			async resendCode() {
				if (this.countdown > 0 || this.isResending) return

				this.isResending = true

				try {
					await api.verificationCode.send({
						email: this.email,
						purpose: 'email_verification'
					})

					this.startCountdown()
					api.showSuccess('Verification code sent')

				} catch (error) {
					console.error('Resend verification code failed:', error)
				} finally {
					this.isResending = false
				}
			},

			async signIn() {
				if (!this.isCodeComplete || this.isLoading) return

				this.isLoading = true

				try {
					// Login with verification code
					const response = await api.auth.login({
						user: {
							email: this.email,
							name: uni.getStorageSync('questionnaire_name'),
							gender: uni.getStorageSync('questionnaire_gender'),
							height: uni.getStorageSync('questionnaire_height'),
							weight: uni.getStorageSync('questionnaire_weight'),
							birthday: uni.getStorageSync('questionnaire_birthday'),
							exercise_frequency: uni.getStorageSync('questionnaire_exercise_frequency'),
							fitness_goals: ['health'], // Default goal, can be customized later
							obstacles: uni.getStorageSync('questionnaire_obstacles')
						},
						verification_code: this.verificationCode
					})

					if (response.success && response.data.token) {
						console.log('登录成功，保存用户信息:', response.data)

						// Save token and user info using API methods
						api.setToken(response.data.token)
						api.setUserInfo(response.data.user)

						// Update store state to sync with API storage
						mutations.setUserInfo(response.data.user)

						console.log('保存后的状态:')
						console.log('- api.getToken():', api.getToken())
						console.log('- api.getUserInfo():', api.getUserInfo())
						console.log('- store.userInfo:', store.userInfo)
						console.log('- store.hasLogin:', store.hasLogin)

						// Clear questionnaire temporary data
						uni.removeStorageSync('login_email')
						uni.removeStorageSync('questionnaire_name')
						uni.removeStorageSync('questionnaire_gender')
						uni.removeStorageSync('questionnaire_height')
						uni.removeStorageSync('questionnaire_weight')
						uni.removeStorageSync('questionnaire_birthday')
						uni.removeStorageSync('questionnaire_exercise_frequency')
						uni.removeStorageSync('questionnaire_obstacles')

						// Show success message
						api.showSuccess('Welcome!')

						// Redirect to user center
						setTimeout(() => {
							console.log('跳转到用户中心页面')
							uni.switchTab({url: '/pages/ucenter/ucenter'})
						}, 1000)
					}

				} catch (error) {
					console.error('Sign in failed:', error)
					// Clear code for retry
					this.fullCode = ''
					setTimeout(() => {
						this.focusHiddenInput()
					}, 100)
				} finally {
					this.isLoading = false
				}
			},

			startCountdown() {
				this.countdown = 60
				const timer = setInterval(() => {
					this.countdown--
					if (this.countdown <= 0) {
						clearInterval(timer)
					}
				}, 1000)
			},

			goBack() {
				uni.navigateBack()
			}
		},
		onLoad() {
			// Get email from storage
			this.email = uni.getStorageSync('login_email')

			// Start countdown
			this.startCountdown()

			// Auto focus input
			setTimeout(() => {
				this.focusHiddenInput()
			}, 300)
		}
	}
</script>

<style lang="scss" scoped>
	.login-verification-container {
		min-height: 100vh;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		padding: 0 40rpx;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 80rpx 0 40rpx;

		.back-btn {
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;

			.back-icon {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}

		.step-indicator {
			flex: 1;
			display: flex;
			justify-content: center;

			.step-bar {
				width: 300rpx;
				height: 8rpx;
				background: #333;
				border-radius: 4rpx;
			}
		}
	}

	.content {
		flex: 1;
		display: flex;
		flex-direction: column;

		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
			line-height: 1.2;
		}

		.subtitle {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 100rpx;
			line-height: 1.4;

			.email-text {
				color: #333;
				font-weight: 600;
			}
		}

		.code-section {
			margin-bottom: 80rpx;
			position: relative;

			.hidden-input {
				position: absolute;
				left: -9999rpx;
				opacity: 0;
				z-index: -1;
			}

			.code-inputs {
				display: flex;
				justify-content: space-between;
				gap: 20rpx;

				.code-input {
					flex: 1;
					height: 120rpx;
					border: 3rpx solid #e0e0e0;
					border-radius: 16rpx;
					background: #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease;
					position: relative;
					cursor: pointer;

					&.active {
						border-color: #333;
						box-shadow: 0 0 0 4rpx rgba(51, 51, 51, 0.1);
					}

					&.filled {
						border-color: #333;
						background: #f8f9fa;
					}

					.digit-text {
						font-size: 48rpx;
						font-weight: bold;
						color: #333;
					}

					.cursor-line {
						position: absolute;
						width: 3rpx;
						height: 60rpx;
						background: #333;
						animation: blink 1s infinite;
					}
				}
			}
		}

		.resend-section {
			text-align: center;

			.resend-text {
				font-size: 28rpx;
				color: #666;
				margin-right: 10rpx;
			}

			.resend-link {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;

				&.disabled {
					color: #999;
				}
			}
		}
	}

	.footer {
		padding: 60rpx 0 80rpx;

		.signin-btn {
			width: 100%;
			height: 120rpx;
			background: #333;
			border: none;
			border-radius: 60rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			transition: all 0.3s ease;

			&.disabled {
				background: #e0e0e0;
				color: #999;
			}

			&:not(.disabled):active {
				background: #555;
			}
		}
	}

	@keyframes blink {

		0%,
		50% {
			opacity: 1;
		}

		51%,
		100% {
			opacity: 0;
		}
	}
</style>