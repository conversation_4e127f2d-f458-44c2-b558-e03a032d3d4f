<template>
	<view class="forgot-password-container">
		<!-- Top Area -->
		<view class="forgot-password-header">
			<view class="logo-wrapper">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			</view>
			<text class="app-name">{{ appConfig.about.appName }}</text>
			<text class="app-slogan">Reset Password</text>
		</view>
		
		<!-- Reset Password Form -->
		<view class="forgot-password-form">
			<!-- Email Input -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.email" 
					:placeholder="$t('common.emailPlaceholder')"
					type="email"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="email" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- Verification Code Input -->
			<view class="input-group verification-group">
				<uni-easyinput 
					v-model="formData.verificationCode" 
					:placeholder="$t('common.verifyCodePlaceholder')"
					type="number"
					trim="both"
					clearable
					:inputBorder="false"
					maxlength="6"
				>
					<template v-slot:left>
						<uni-icons type="checkmarkempty" size="22" color="#999"></uni-icons>
					</template>
					<template v-slot:right>
						<button 
							class="verification-btn" 
							:class="{ 'verification-btn-disabled': countdown > 0 || !canSendCode }"
							:disabled="countdown > 0 || !canSendCode"
							@click="sendVerificationCode"
						>
							{{ countdown > 0 ? `${countdown}s` : $t('common.getVerifyCode') }}
						</button>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- New Password Input -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.newPassword" 
					:placeholder="$t('common.newPasswordPlaceholder')"
					type="password"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="locked" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- Confirm New Password Input -->
			<view class="input-group">
				<uni-easyinput 
					v-model="formData.confirmNewPassword" 
					:placeholder="$t('common.confirmNewPasswordPlaceholder')"
					type="password"
					trim="both"
					clearable
					:inputBorder="false"
				>
					<template v-slot:left>
						<uni-icons type="locked" size="22" color="#999"></uni-icons>
					</template>
				</uni-easyinput>
			</view>
			
			<!-- Reset Password Button -->
			<button 
				class="reset-btn" 
				:class="{ 'reset-btn-disabled': !canReset }"
				:disabled="!canReset"
				@click="handleResetPassword"
			>
				Reset Password
			</button>
			
			<!-- Feature Links -->
			<view class="forgot-password-links">
				<text class="link-text" @click="goToLogin">
					Back to Login
				</text>
				<text class="link-text" @click="goToRegister">
					Register New Account
				</text>
			</view>
		</view>
		
		<!-- Bottom Tips -->
		<view class="forgot-password-footer">
			<text class="help-text">
				We will send a verification code to your email. Please check your inbox and enter the code to reset your password
			</text>
		</view>
	</view>
</template>

<script>
	import api from '@/common/api.js'

	export default {
		data() {
			return {
				formData: {
					email: '',
					verificationCode: '',
					newPassword: '',
					confirmNewPassword: ''
				},
				countdown: 0,
				timer: null,
				isLoading: false
			}
		},
		computed: {
			appConfig() {
				return getApp().globalData.config
			},
			canSendCode() {
				return this.formData.email.trim() && this.validateEmail(this.formData.email)
			},
			canReset() {
				return this.formData.email.trim() && 
					   this.formData.verificationCode.trim() && 
					   this.formData.newPassword.trim() && 
					   this.formData.confirmNewPassword.trim() && 
					   !this.isLoading
			}
		},
		methods: {
			/**
			 * Send verification code
			 */
			async sendVerificationCode() {
				if (!this.canSendCode || this.countdown > 0) return
				
				try {
					const response = await api.auth.sendPasswordResetCode(this.formData.email.trim())
					
					if (response.success) {
						api.showSuccess('Verification code sent to your email')
						this.startCountdown()
					}
				} catch (error) {
					console.error('Failed to send verification code:', error)
					// Error handling is done in api.js
				}
			},
			
			/**
			 * Start countdown
			 */
			startCountdown() {
				this.countdown = 60
				this.timer = setInterval(() => {
					this.countdown--
					if (this.countdown <= 0) {
						clearInterval(this.timer)
						this.timer = null
					}
				}, 1000)
			},
			
			/**
			 * Handle password reset
			 */
			async handleResetPassword() {
				if (!this.canReset) return
				
				// Email format validation
				if (!this.validateEmail(this.formData.email)) {
					api.showError('Please enter a valid email address')
					return
				}
				
				// Verification code validation
				if (this.formData.verificationCode.length !== 6) {
					api.showError('Please enter a 6-digit verification code')
					return
				}
				
				// Password validation
				if (!this.validatePassword(this.formData.newPassword)) {
					api.showError('Password must be 6-20 characters long')
					return
				}
				
				// Confirm password validation
				if (this.formData.newPassword !== this.formData.confirmNewPassword) {
					api.showError('Passwords do not match')
					return
				}
				
				this.isLoading = true
				
				try {
					// Call reset password API
					const response = await api.auth.resetPassword({
						email: this.formData.email.trim(),
						code: this.formData.verificationCode.trim(),
						new_password: this.formData.newPassword.trim(),
						new_password_confirmation: this.formData.confirmNewPassword.trim()
					})
					
					// Reset successful
					if (response.success) {
						api.showSuccess(response.message || 'Password reset successful')
						
						// Delayed redirect to login page
						setTimeout(() => {
							this.goToLogin()
						}, 1500)
					}
				} catch (error) {
					console.error('Password reset failed:', error)
					// Error handling is done in api.js
				} finally {
					this.isLoading = false
				}
			},
			
			/**
			 * Validate email format
			 */
			validateEmail(email) {
				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
				return emailRegex.test(email)
			},
			
			/**
			 * Validate password format
			 */
			validatePassword(password) {
				return password.length >= 6 && password.length <= 20
			},
			
			/**
			 * Go to login page
			 */
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			},
			
			/**
			 * Go to register page
			 */
			goToRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			}
		},
		beforeDestroy() {
			// Clear timer
			if (this.timer) {
				clearInterval(this.timer)
				this.timer = null
			}
		}
	}
</script>

<style lang="scss">
	.forgot-password-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 0 40rpx;
		box-sizing: border-box;
	}
	
	.forgot-password-header {
		text-align: center;
		margin-bottom: 80rpx;
		
		.logo-wrapper {
			margin-bottom: 40rpx;
			
			.logo {
				width: 120rpx;
				height: 120rpx;
				border-radius: 20rpx;
				box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
			}
		}
		
		.app-name {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			margin-bottom: 20rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.app-slogan {
			display: block;
			font-size: 32rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 300;
		}
	}
	
	.forgot-password-form {
		width: 100%;
		max-width: 640rpx;
		margin-bottom: 60rpx;
		
		.input-group {
			margin-bottom: 30rpx;
			background: rgba(255, 255, 255, 0.95);
			border-radius: 25rpx;
			padding: 5rpx 30rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			backdrop-filter: blur(10rpx);
			
			&.verification-group {
				padding-right: 5rpx;
			}
			
			:deep(.uni-easyinput) {
				
				.uni-easyinput__content {
					height: 90rpx;
					
					.uni-easyinput__content-input {
						font-size: 32rpx;
						color: #333;
					}
				}
			}
			
			.verification-btn {
				background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
				border: none;
				border-radius: 20rpx;
				padding: 15rpx 30rpx;
				font-size: 24rpx;
				color: #fff;
				margin: 10rpx;
				
				&.verification-btn-disabled {
					background: #ccc;
				}
			}
		}
		
		.reset-btn {
			width: 100%;
			height: 90rpx;
			background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
			border: none;
			border-radius: 25rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #fff;
			box-shadow: 0 8rpx 25rpx rgba(79, 172, 254, 0.4);
			margin-top: 40rpx;
			margin-bottom: 40rpx;
			
			&.reset-btn-disabled {
				background: #ccc;
				box-shadow: none;
			}
		}
		
		.forgot-password-links {
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.link-text {
				color: rgba(255, 255, 255, 0.9);
				font-size: 28rpx;
				text-decoration: underline;
			}
		}
	}
	
	.forgot-password-footer {
		text-align: center;
		padding: 0 40rpx;
		
		.help-text {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			line-height: 1.6;
		}
	}
</style>