/**
 * Deep Sport API 工具方法
 * 统一处理 token、错误、base url、国际化
 */

import i18n from '@/lang/i18n.js'

// 监听语言切换事件，确保 API 请求使用最新的语言设置
uni.$on('changeLanguage', () => {
  console.log('API工具：检测到语言切换，将在下次请求时更新Accept-Language头部')
})

/**
 * 获取当前语言
 */
const getCurrentLanguage = () => {
  try {
    return uni.getStorageSync('CURRENT_LANG') || 'en'
  } catch (e) {
    console.warn('获取当前语言失败:', e)
    return 'en'
  }
}

/**
 * 获取 Accept-Language 头部值
 */
const getAcceptLanguage = () => {
  const currentLang = getCurrentLanguage()
  
  // 将 uni-starter 的语言代码转换为标准的 Accept-Language 格式
  const languageMap = {
    'zh-Hans': 'zh-CN,zh;q=0.9,en;q=0.8',
    'en': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7'
  }
  
  return languageMap[currentLang] || languageMap['zh-Hans']
}
const $t = (key, defaultText = '') => {
  try {
    // #ifdef VUE3
    return i18n.global.t(key) || defaultText
    // #endif
    
    // #ifndef VUE3
    return i18n.t(key) || defaultText
    // #endif
  } catch (e) {
    console.warn('国际化文本获取失败:', key, e)
    return defaultText
  }
}

// API 配置
const API_CONFIG = {
  // 开发环境
  dev: {
    baseUrl: 'http://***********:3000',
    timeout: 30000
  },
  // 生产环境
  prod: {
    baseUrl: 'https://app-api.xiancheng.online',
    timeout: 30000
  }
}

// 获取当前环境配置
const getConfig = () => {
  let isDev = false // 默认开发环境
  
  // #ifdef H5
  isDev = window.location.hostname === 'localhost' || window.location.hostname.includes('192.168')
  // #endif
  
  return isDev ? API_CONFIG.dev : API_CONFIG.prod
}

/**
 * 获取存储的 token
 */
const getToken = () => {
  try {
    return uni.getStorageSync('auth_token') || ''
  } catch (e) {
    console.error('获取token失败:', e)
    return ''
  }
}

/**
 * 保存 token
 */
const setToken = (token) => {
  try {
    uni.setStorageSync('auth_token', token)
  } catch (e) {
    console.error('保存token失败:', e)
  }
}

/**
 * 清除认证信息 (包括token和用户信息)
 */
const clearAuth = () => {
  try {
    uni.removeStorageSync('auth_token')
    clearUserInfo()
  } catch (e) {
    console.error('清除认证信息失败:', e)
  }
}

/**
 * 保存用户信息 (与store保持一致)
 */
const setUserInfo = (userInfo) => {
  try {
    // 同时保存到两个位置，确保兼容性
    uni.setStorageSync('user_info', userInfo)
    uni.setStorageSync('uni-id-pages-userInfo', userInfo)
  } catch (e) {
    console.error('保存用户信息失败:', e)
  }
}

/**
 * 获取用户信息 (优先从store位置获取)
 */
const getUserInfo = () => {
  try {
    let userInfo = uni.getStorageSync('uni-id-pages-userInfo')
    if (!userInfo) {
      userInfo = uni.getStorageSync('user_info')
    }
    return userInfo || null
  } catch (e) {
    console.error('获取用户信息失败:', e)
    return null
  }
}

/**
 * 清除用户信息 (清除所有位置)
 */
const clearUserInfo = () => {
  try {
    uni.removeStorageSync('user_info')
    uni.removeStorageSync('uni-id-pages-userInfo')
  } catch (e) {
    console.error('清除用户信息失败:', e)
  }
}

/**
 * 显示错误提示
 */
const showError = (message) => {
  const errorMsg = message || $t('api.operationFailed', '操作失败')
  uni.showToast({
    title: errorMsg,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示成功提示
 */
const showSuccess = (message) => {
  const successMsg = message || $t('api.operationSuccess', '操作成功')
  uni.showToast({
    title: successMsg,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示加载中
 */
const showLoading = (title) => {
  const loadingTitle = title || $t('api.loading', '加载中...')
  uni.showLoading({
    title: loadingTitle,
    mask: true
  })
}

/**
 * 隐藏加载
 */
const hideLoading = () => {
  uni.hideLoading()
}

/**
 * 处理认证错误
 */
const handleAuthError = () => {
  clearAuth()
  // 获取当前页面路径
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    console.info('当前页面路径:', currentPage.route)
    
    // 如果当前已在欢迎页或登录验证页，则不进行跳转
    const exemptRoutes = [
      'pages/welcome/welcome',
      'pages/login/verification'
    ]
    
    if (exemptRoutes.includes(currentPage.route)) {
      return
    }
  }
  showError($t('api.loginExpired', '登录已过期，请重新登录'))
  // 延迟跳转到登录页，避免与当前操作冲突
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/login/email'
    })
  }, 1500)
}

/**
 * API 请求方法
 */
const request = (options) => {
  const config = getConfig()
  const token = getToken()
  
  // 默认选项
  const defaultOptions = {
    method: 'GET',
    timeout: config.timeout,
    header: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': getAcceptLanguage()
    },
    dataType: 'json'
  }
  
  // 合并选项
  const requestOptions = {
    ...defaultOptions,
    ...options,
    url: config.baseUrl + options.url,
    header: {
      ...defaultOptions.header,
      ...options.header
    }
  }
  
  // 添加认证头
  if (token) {
    requestOptions.header.Authorization = `Bearer ${token}`
  }
  
  // 显示加载提示
  if (options.showLoading !== false) {
    showLoading(options.loadingTitle)
  }
  
  return new Promise((resolve, reject) => {
    uni.request({
      ...requestOptions,
      success: (res) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          hideLoading()
        }
        
        const { statusCode, data } = res
        
        // HTTP 状态码检查
        if (statusCode >= 200 && statusCode < 300) {
          // 检查业务状态
          if (data && typeof data === 'object') {
            if (data.success === false) {
              // 业务错误
              const errorMessage = data.message || data.error || '操作失败'
              
              if (options.showError !== false) {
                showError(errorMessage)
              }
              
              reject(new Error(errorMessage))
            } else {
              // 成功
              if (options.showSuccess && data.message) {
                showSuccess(data.message)
              }
              resolve(data)
            }
          } else {
            // 数据格式异常
            if (options.showError !== false) {
              showError($t('api.dataFormatError', '数据格式错误'))
            }
            reject(new Error($t('api.dataFormatError', '数据格式错误')))
          }
        } else if (statusCode === 401) {
          // 认证错误
          hideLoading()
          handleAuthError()
          reject(new Error($t('api.authFailed', '认证失败')))
        } else {
          // 其他 HTTP 错误
          hideLoading()
          const errorMessage = data?.message || data?.error || $t('api.serverError', `服务器错误 (${statusCode})`)
          
          if (options.showError !== false) {
            showError(errorMessage)
          }
          
          reject(new Error(errorMessage))
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          hideLoading()
        }
        
        console.error('API请求失败:', err)
        
        let errorMessage = $t('api.networkError', '网络请求失败')
        
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = $t('api.timeout', '请求超时，请检查网络连接')
          } else if (err.errMsg.includes('fail')) {
            errorMessage = $t('api.connectionFailed', '网络连接失败，请检查网络设置')
          }
        }
        
        if (options.showError !== false) {
          showError(errorMessage)
        }
        
        reject(new Error(errorMessage))
      }
    })
  })
}

// GET 请求
const get = (url, params = {}, options = {}) => {
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return request({
    url: fullUrl,
    method: 'GET',
    ...options
  })
}

// POST 请求
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT 请求
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// PATCH 请求
const patch = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PATCH',
    data,
    ...options
  })
}

// DELETE 请求
const del = (url, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    ...options
  })
}

/**
 * 文件上传
 */
const uploadFile = (url, filePath, options = {}) => {
  const config = getConfig()
  const token = getToken()
  
  const defaultOptions = {
    name: 'file',
    header: {
      'Accept-Language': getAcceptLanguage()
    }
  }
  
  const uploadOptions = {
    ...defaultOptions,
    ...options,
    url: config.baseUrl + url,
    filePath
  }
  
  // 添加认证头
  if (token) {
    uploadOptions.header.Authorization = `Bearer ${token}`
  }
  
  // 显示加载提示
  if (options.showLoading !== false) {
    showLoading(options.loadingTitle || $t('api.uploading', '上传中...'))
  }
  
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      ...uploadOptions,
      success: (res) => {
        hideLoading()
        
        try {
          const data = JSON.parse(res.data)
          
          if (data.success === false) {
            const errorMessage = data.message || data.error || $t('api.uploadFailed', '上传失败')
            if (options.showError !== false) {
              showError(errorMessage)
            }
            reject(new Error(errorMessage))
          } else {
            if (options.showSuccess && data.message) {
              showSuccess(data.message)
            }
            resolve(data)
          }
        } catch (e) {
          if (options.showError !== false) {
            showError($t('api.dataFormatError', '上传响应格式错误'))
          }
          reject(new Error($t('api.dataFormatError', '上传响应格式错误')))
        }
      },
      fail: (err) => {
        hideLoading()
        console.error('文件上传失败:', err)
        
        const errorMessage = $t('api.uploadFailed', '文件上传失败')
        if (options.showError !== false) {
          showError(errorMessage)
        }
        
        reject(new Error(errorMessage))
      }
    })
  })
}

// 导出 API 方法
export default {
  // 基础请求方法
  request,
  get,
  post,
  put,
  patch,
  delete: del,
  uploadFile,
  
  // 认证相关
  getToken,
  setToken,
  clearAuth,
  setUserInfo,
  getUserInfo,
  clearUserInfo,
  
  // 语言相关
  getCurrentLanguage,
  getAcceptLanguage,
  
  // 提示方法
  showError,
  showSuccess,
  showLoading,
  hideLoading,
  
  // 国际化
  $t,
  
  // 配置
  getConfig,
  
  // 法律文档相关API
  legal: {
    /**
     * 获取隐私政策
     */
    getPrivacyPolicy() {
      return get('/legal_documents/privacy_policy')
    },
    
    /**
     * 获取服务条款（用于用户协议）
     */
    getTermsOfService() {
      return get('/legal_documents/terms_of_service')
    }
  },
  
  // 用户认证相关API
  auth: {
    /**
     * 用户登录
     */
    login(credentials) {
      return post('/auth/login', credentials)
    },
    
    /**
     * 用户注册
     */
    register(data) {
      return post('/auth/register', data)
    },
    
    /**
     * 发送注册验证码
     */
    sendRegisterCode(email) {
      return post('/verification_codes/send', { email,purpose:'email_verification' })
    },
    
    /**
     * 发送密码重置验证码
     */
    sendPasswordResetCode(email) {
      return post('/auth/password/send_reset_code', { email })
    },
    
    /**
     * 重置密码
     */
    resetPassword(data) {
      return post('/auth/password/reset', data)
    },
    
    /**
     * 退出登录
     */
    logout() {
      return del('/auth/logout')
    },
    
    /**
     * 删除账户
     */
    deleteAccount() {
      return del('/auth/delete_account')
    },
    
    /**
     * 更新用户资料
     */
    updateProfile(data) {
      return patch(`/users/${data.id}/profile`, data)
    },
    
    /**
     * 刷新token
     */
    refreshToken() {
      return post('/auth/refresh')
    }
  },
  
  // 验证码相关API
  verificationCode: {
    /**
     * 发送验证码
     */
    send(data) {
      return post('/verification_codes/send', data)
    },
    
    /**
     * 验证验证码
     */
    verify(data) {
      return post('/verification_codes/verify', data)
    }
  },
  
  // 用户相关API
  users: {
    /**
     * 获取问卷选项数据
     */
    getQuestionnaireOptions() {
      return get('/users/questionnaire_options')
    },
    
    /**
     * 更新用户个人资料
     */
    updateProfile(data) {
      return patch('/users/me/profile', { user: data })
    },
    
    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
      return get('/auth/me')
    }
  },
  
  // 问卷相关API
  questionnaire: {
    /**
     * 获取问卷选项
     */
    getOptions() {
      return get('/users/questionnaire_options')
    },
    
    /**
     * 提交问卷数据
     */
    submit(data) {
      return patch('/users/me/profile', { user: data })
    }
  },
  
  // 运动分析相关API
  exerciseAnalysis: {
    /**
     * 获取用户的所有运动分析历史记录
     * @param {Object} params - 查询参数（可选）
     * @param {Number} params.page - 页码
     * @param {Number} params.per_page - 每页数量
     */
    getAnalyses(params = {}, options = {}) {
      return get('/exercise_analyses', params, options)
    },
    
    /**
     * 获取单个运动分析详情
     * @param {String} id - 运动分析ID
     */
    getAnalysisDetail(id, options = {}) {
      return get(`/exercise_analyses/${id}`, {}, options)
    },
    
    /**
     * 创建新的运动分析请求（支持多图片上传）
     * @param {Array} filePaths - 图片文件路径数组
     */
    createAnalysis(filePaths, options = {}) {
      // 由于需要上传多张图片，使用 FormData
      const config = getConfig()
      const token = getToken()
      
      const uploadOptions = {
        header: {
          'Content-Type': 'multipart/form-data',
          'Accept-Language': getAcceptLanguage()
        },
        showLoading: options.showLoading !== false,
        loadingTitle: options.loadingTitle || $t('api.uploading', '上传中...'),
        showError: options.showError !== false,
        showSuccess: options.showSuccess
      }
      
      // 添加认证头
      if (token) {
        uploadOptions.header.Authorization = `Bearer ${token}`
      }
      
      return new Promise((resolve, reject) => {
        // 构建文件数组，用于一次上传多张图片
        const files = filePaths.map((filePath, index) => ({
          name: `images[${index}]`,
          uri: filePath
        }))
        
        // 一次性上传所有图片
        uni.uploadFile({
          url: config.baseUrl + '/exercise_analyses',
          files: files,
          header: uploadOptions.header,
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.success === false) {
                const errorMessage = data.message || data.error || $t('api.uploadFailed', '上传失败')
                if (uploadOptions.showError !== false) {
                  showError(errorMessage)
                }
                reject(new Error(errorMessage))
              } else {
                if (uploadOptions.showSuccess && data.message) {
                  showSuccess(data.message)
                }
                resolve(data)
              }
            } catch (e) {
              if (uploadOptions.showError !== false) {
                showError($t('api.dataFormatError', '上传响应格式错误'))
              }
              reject(new Error($t('api.dataFormatError', '上传响应格式错误')))
            }
          },
          fail: (err) => {
            console.error('文件上传失败:', err)
            
            const errorMessage = $t('api.uploadFailed', '文件上传失败')
            if (uploadOptions.showError !== false) {
              showError(errorMessage)
            }
            
            reject(new Error(errorMessage))
          }
        })
      })
    },
    
    /**
     * 删除运动分析记录
     * @param {String} id - 运动分析ID
     */
    deleteAnalysis(id, options = {}) {
      return del(`/exercise_analyses/${id}`, options)
    }
  }
}