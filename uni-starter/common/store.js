import pagesJson from '@/pages.json'
import api from '@/common/api.js'

let hostUserInfo = uni.getStorageSync('uni-id-pages-userInfo') || {}

const data = {
	userInfo: hostUserInfo,
	hasLogin: Object.keys(hostUserInfo).length != 0
}

// 定义 mutations, 修改属性
export const mutations = {
	// data不为空，表示传递要更新的值(注意不是覆盖是合并),什么也不传时，直接查库获取更新
	async updateUserInfo(data = false) {
		if (data) {
			try {
				// 调用我们的用户资料更新接口
				const response = await api.users.updateProfile(data)
				if (response.success) {
					uni.showToast({
						title: "更新成功",
						icon: 'none',
						duration: 3000
					});
					this.setUserInfo(response.data.user)
				} else {
					uni.showToast({
						title: response.message || "更新失败",
						icon: 'none',
						duration: 3000
					});
				}
			} catch (error) {
				console.error('Update user info failed:', error)
				uni.showToast({
					title: "更新失败",
					icon: 'none',
					duration: 3000
				});
			}
		} else {
			// 从服务器获取最新用户信息
			try {
				const response = await api.users.getCurrentUser()
				if (response.success && response.data.user) {
					this.setUserInfo(response.data.user)
				} else {
					// 如果获取失败，可能token已过期，清除用户信息
					this.setUserInfo({}, {cover: true})
				}
			} catch (error) {
				console.error('Get user info failed:', error)
				this.setUserInfo({}, {cover: true})
			}
		}
	},
	
	setUserInfo(data, {cover} = {cover: false}) {
		console.log('set-userInfo', data);
		let userInfo = cover ? data : Object.assign(store.userInfo, data)
		store.userInfo = Object.assign({}, userInfo)
		store.hasLogin = Object.keys(store.userInfo).length != 0
		console.log('store.userInfo', store.userInfo);
		uni.setStorageSync('uni-id-pages-userInfo', store.userInfo)
		return data
	},
	
	async logout() {
		try {
			// 调用后端登出接口
			await api.auth.logout()
		} catch (error) {
			console.error('Logout failed:', error)
		}
		
		// 清除本地存储 (使用API方法确保一致性)
		api.clearAuth()
		this.setUserInfo({}, {cover: true})
		
		// 发送登出事件
		uni.$emit('uni-id-pages-logout')
		
		// 重定向到登录页面
		uni.redirectTo({
			url: `/${pagesJson.uniIdRouter && pagesJson.uniIdRouter.loginPage ? pagesJson.uniIdRouter.loginPage : 'pages/welcome/welcome'}`,
		});
	},
	
	loginBack(e = {}) {
		const {uniIdRedirectUrl = ''} = e
		let delta = 0; //判断需要返回几层
		let pages = getCurrentPages();
		
		pages.forEach((page, index) => {
			if (pages[pages.length - index - 1].route.split('/')[3] == 'login') {
				delta++
			}
		})
		
		console.log('判断需要返回几层:', delta);
		
		if (uniIdRedirectUrl) {
			return uni.redirectTo({
				url: uniIdRedirectUrl,
				fail: (err1) => {
					uni.switchTab({
						url: uniIdRedirectUrl,
						fail: (err2) => {
							console.log(err1, err2)
						}
					})
				}
			})
		}
		
		// #ifdef H5
		if (e.loginType == 'weixin') {
			console.log('window.history', window.history);
			return window.history.go(-3)
		}
		// #endif

		if (delta) {
			const page = pagesJson.pages[0]
			return uni.reLaunch({
				url: `/${page.path}`
			})
		}

		uni.navigateBack({
			delta
		})
	},
	
	loginSuccess(e = {}) {
		const {
			showToast = true, 
			toastText = '登录成功', 
			autoBack = true, 
			uniIdRedirectUrl = '', 
			passwordConfirmed,
			user,
			token
		} = e
		
		console.log({toastText, autoBack});
		
		if (showToast) {
			uni.showToast({
				title: toastText,
				icon: 'none',
				duration: 3000
			});
		}
		
		// 保存用户信息和token
		if (user) {
			this.setUserInfo(user)
		}
		if (token) {
			api.setToken(token)
		}
		
		// 异步调用（更新用户信息）防止获取头像等操作阻塞页面返回
		this.updateUserInfo()

		uni.$emit('uni-id-pages-login-success')

		// 移除设置密码逻辑，因为我们使用验证码登录
		if (autoBack) {
			this.loginBack({uniIdRedirectUrl})
		}
	}
}

// #ifdef VUE2
import Vue from 'vue'
// 通过Vue.observable创建一个可响应的对象
export const store = Vue.observable(data)
// #endif

// #ifdef VUE3
import {
	reactive
} from 'vue'
// 通过Vue.observable创建一个可响应的对象
export const store = reactive(data)
// #endif
