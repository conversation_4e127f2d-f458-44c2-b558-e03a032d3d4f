{
	"pages": [{
			"path": "pages/welcome/welcome",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#667eea"
			}
		},
		{
			"path": "pages/list/list",
			"style": {
				// #ifndef APP
				"enablePullDownRefresh": true,
				// #endif
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/grid/grid",
			"style": {
              // #ifndef APP
              "enablePullDownRefresh": true,
              // #endif
              "navigationStyle": "custom"
			}
		}, 
		{
			"path": "pages/ucenter/ucenter",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "Login",
				"navigationBarBackgroundColor": "#667eea",
				"navigationBarTextStyle": "white",
				"backgroundColor": "#667eea"
			}
		},
		{
			"path": "pages/register/register",
			"style": {
				"navigationBarTitleText": "Register",
				"navigationBarBackgroundColor": "#764ba2",
				"navigationBarTextStyle": "white",
				"backgroundColor": "#764ba2"
			}
		},
		{
			"path": "pages/forgot-password/forgot-password",
			"style": {
				"navigationBarTitleText": "Forgot Password",
				"navigationBarBackgroundColor": "#f5576c",
				"navigationBarTextStyle": "white",
				"backgroundColor": "#f5576c"
			}
		},
		{
			"path": "pages/legal/legal",
			"style": {
				"navigationBarTitleText": "Legal Documents",
				"navigationBarBackgroundColor": "#667eea",
				"navigationBarTextStyle": "white",
				"backgroundColor": "#f8f9fa"
			}
		},
		{
			"path": "pages/questionnaire/gender",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/questionnaire/measurements",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/questionnaire/birthday",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/questionnaire/exercise-frequency",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/questionnaire/obstacles",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/questionnaire/personalization",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/login/email",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/login/verification",
			"style": {
				"navigationStyle": "custom",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/uni-agree/uni-agree",
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"popGesture": "none"
				}
			}
		},
		{
			"path": "pages/ucenter/read-news-log/read-news-log",
			"style": {
				"navigationBarTitleText": "Reading History",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/ucenter/personal-details/personal-details",
			"style": {
				"navigationBarTitleText": "Personal Details"
			}
		}
		// #ifdef APP
		, {
			"path": "pages/ucenter/about/about",
			"style": {
				"navigationBarTitleText": "About",
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"type": "share"
						}]
					}
				}
			}
		},
		{
			"path": "pages/ucenter/invite/invite",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		}
		// #endif
	],
	"subPackages": [
	],
	"globalStyle": {
		// #ifdef H5
		"h5": {
			"titleNView": false
		},
		// #endif
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-starter",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F8F8F8",
		"enablePullDownRefresh": false,
		// "maxWidth":375,
		"rpxCalcMaxDeviceWidth": 375,
		"rpxCalcBaseDeviceWidth": 375
		// "rpxCalcIncludeWidth":0
	},
	"condition": {
		"list": [{
				"path": "pages/list/detail"
			}, {
				"path": "pages/list/list"
			},
			{
				"path": "pages/ucenter/settings/settings"
			}
		],
		"current": 1
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#667eea",
		"borderStyle": "black",
		"backgroundColor": "#FFFFFF",
		"list": [{
			"pagePath": "pages/list/list",
			"iconPath": "static/tabbar/list.png",
			"selectedIconPath": "static/tabbar/list_active.png",
			"text": "Home"
		}, {
			"pagePath": "pages/grid/grid",
			"iconPath": "static/tabbar/grid.png",
			"selectedIconPath": "static/tabbar/grid_active.png",
			"text": "Analysis"
		}, {
			"pagePath": "pages/ucenter/ucenter",
			"iconPath": "static/tabbar/me.png",
			"selectedIconPath": "static/tabbar/me_active.png",
			"text": "Profile"
		}]
	}
}