export default {
	tabbar:'列表,宫格,通讯录,我的',
	agreementsTitle:'用户服务协议,隐私政策',
	common:{
		wechatFriends: "微信好友",
		wechatBbs: "微信朋友圈",
		weibo:"微博",
		more: "更多",
		agree:"同意",
		copy: "复制",
		wechatApplet: "微信小程序",
		cancelShare: "取消分享",
		updateSucceeded: "更新成功",
		phonePlaceholder: "请输入手机号",
		emailPlaceholder: "请输入邮箱",
		verifyCodePlaceholder: "请输入验证码",
		newPasswordPlaceholder: "请输入新密码",
		confirmNewPasswordPlaceholder: "请确认新密码",
		confirmPassword: "请确认密码",
		verifyCodeSend: "验证码已通过短信发送至",
		passwordDigits: "密码为6 - 20位",
		getVerifyCode: "获取验证码",
		noAgree: "你未同意隐私政策协议",
		gotIt: "知道了",
		login: "登录",
		error: "错误",
		complete: "完成",
		submit: "提交",
		formatErr:"手机号码格式不正确",
		sixDigitCode:"请输入6位验证码",
		resetNavTitle:"重置密码",
        unknown: "未知",
        male: "男",
        female: "女",
        occasionally: "偶尔锻炼 (0-2次/周)",
        regular: "定期锻炼 (3-5次/周)",
        athlete: "专业运动员 (6+次/周)",
        loseWeight: "减重",
        gainMuscle: "增肌",
        improveEndurance: "提高耐力",
        increaseStrength: "增强力量",
        improveFlexibility: "提高柔韧性",
        maintainHealth: "保持健康",
        sportsPerformance: "运动表现",
        rehabilitation: "康复训练",
        lackTime: "缺乏时间",
        lackMotivation: "缺乏动力",
        lackKnowledge: "缺乏知识",
        lackEquipment: "缺乏设备",
        lackSpace: "缺乏场地",
        physicalLimitations: "身体限制",
        workSchedule: "工作安排",
        familyResponsibilities: "家庭责任",
        financialConstraints: "经济限制",
        weatherConditions: "天气条件",
        underweight: "偏瘦",
        normal: "正常",
        overweight: "超重",
        obese: "肥胖",
		
	},
	list: {
		inputPlaceholder: "请输入搜索内容",
	},
	search:{
		cancelText: '取消',
		searchHistory: "搜索历史",
		searchDiscovery: "搜索发现",
		deleteAll: "全部删除",
		delete: "删除",
		deleteTip: "确认清空搜索历史吗？",
		complete: "完成",
		searchHiddenTip: "当前搜索发现已隐藏",
	},
	grid:{
		grid: "宫格组件",
		visibleToAll: "所有人可见",
		invisibleToTourists: "游客不可见",
		adminVisible: "管理员可见",
		clickTip: "点击第",
		clickTipGrid: "个宫格",
	},
	mine:{
		showText: "文字",
		signIn: "普通签到",
		signInByAd:"看广告签到",
		toEvaluate: "去评分",
		readArticles: "阅读过的文章",
		myScore: "我的积分",
		invite: "分销推荐",
		feedback: "问题与反馈",
		settings: "设置",
		checkUpdate: "检查更新",
		about: "关于",
		clicked: "你点击了",
		checkScore: "请登录后查看积分",
		currentScore: "当前积分为",
		noScore: "当前无积分",
		notLogged: "未登录",
	},
	userinfo:{
		navigationBarTitle:"个人资料",
		ProfilePhoto: "头像",
		nickname: "昵称",
		notSet: "未设置",
		phoneNumber: "手机号",
		notSpecified: "未绑定",
		setNickname: "设置昵称",
		setNicknamePlaceholder: "请输入要设置的昵称",
		bindPhoneNumber: "本机号码一键绑定",
		bindOtherLogin: "其他号码绑定",
		noChange: "没有变化",
		uploading: "正在上传",
		requestFail: "请求服务失败",
		setting: "设置中",
		deleteSucceeded: "删除成功",
		setSucceeded: "设置成功",
	},
	smsCode:{
		resendVerifyCode: "重新发送",
		phoneErrTip: "手机号格式错误",
		sendSuccessTip: "短信验证码发送成功",
	},
	loadMore:{
		noData: "暂无数据",
		noNetwork: "网络异常",
		toSet: "前往设置",
		error: "错误",
	},
	uniFeedback:{
		navigationBarTitle:"问题与反馈",
		msgTitle: "留言内容",
		imgTitle: "图片列表",
		contacts: "联系人",
		phone: "联系电话",
		submit: "提交",
	},
	settings:{
		navigationBarTitle:"设置",
		userInfo: "账号资料",
		personalDetails: "个人详情",
		changePassword: "修改密码",
		clearTmp: "清理缓存",
		pushServer: "推送功能",
		fingerPrint: "指纹解锁",
		facial: "人脸解锁",
		deactivate: "注销账号",
		deleteAccount: "删除账户",
		logOut: "退出登录",
		login: "登录",
		failTip: "认证失败请重试",
		authFailed: "认证失败",
		changeLanguage: "切换语言",
		please: "请用",
		successText: "成功",
		deviceNoOpen: "设备未开启",
		fail: "失败",
		tips: "提示",
		exitLogin: "是否退出登录?",
		deleteAccountConfirm: "删除账户确认",
		deleteAccountWarning: "确定要删除账户吗？此操作将永久删除您的账户和所有相关数据，且无法恢复！",
		deleteAccountPasswordTip: "请输入您的密码以确认删除账户：",
		passwordPlaceholder: "请输入当前密码",
		clearing: "清除中",
		clearedSuccessed: "清除成功",
		deleting: "删除中...",
		deleteSuccess: "账户删除成功",
		deleteFailed: "账户删除失败",
		finalDeleteConfirm: "确定要删除账户吗？此操作无法撤销！",
		confirmText: "确定",
		cancelText: '取消',
		// 个人详情页面
		personalDetailsTitle: "个人详情",
		basicInfo: "基本信息",
		bodyInfo: "身体信息",
		fitnessInfo: "运动信息",
		name: "姓名",
		namePlaceholder: "请输入姓名",
		email: "邮箱",
		emailPlaceholder: "请输入邮箱",
		gender: "性别",
		genderPlaceholder: "请选择性别",
		birthday: "生日",
		birthdayPlaceholder: "请选择生日",
		height: "身高 (cm)",
		heightPlaceholder: "请输入身高",
		weight: "体重 (kg)",
		weightPlaceholder: "请输入体重",
		bmi: "BMI",
		exerciseFrequency: "运动频率",
		exerciseFrequencyPlaceholder: "请选择运动频率",
		fitnessGoals: "健身目标",
		obstacles: "阻碍因素",
		updateProfile: "更新资料",
		updateSuccess: "更新成功",
		updateFailed: "更新失败",
		loading: "加载中...",
		updating: "更新中...",
		loadFailed: "加载失败",
	},
	deactivate:{
		cancelText: '取消',
		nextStep: "下一步",
		navigationBarTitle:"注销提示"
	},
	about:{
		sacnQR: "扫描二维码，您的朋友也可以下载",
		client: "客户端",
		and: "和",
		about: "关于",
	},
	invite:{
		download: "下载",
	},
	login:{
		phoneLogin: "登录后即可展示自己",
		phoneLoginTip: "未注册的手机号验证通过后将自动注册",
		getVerifyCode: "获取验证码",
	},
	uniQuickLogin:{
		accountLogin: "账号登录",
		SMSLogin: "短信验证码",
		wechatLogin: "微信登录",
		appleLogin: "苹果登录",
		oneClickLogin: "一键登录",
		QQLogin: "QQ登录",
		xiaomiLogin: "小米登录",
		getProviderFail: "获取服务供应商失败",
		loginErr: "登录服务初始化错误",
		chooseOtherLogin: "点击了第三方登录",
	},
	pwdLogin:{
		pwdLogin: "用户名密码登录",
		placeholder: "请输入手机号/用户名",
		passwordPlaceholder: "请输入密码",
		verifyCodePlaceholder: "请输入验证码",
		login: "登录",
		forgetPassword: "忘记密码",
		register: "注册账号",
	},
	register:{
		navigationBarTitle:"注册",
		usernamePlaceholder: "请输入用户名",
		nicknamePlaceholder: "请输入用户昵称",
		registerAndLogin: "注册并登录",
		passwordDigitsPlaceholder: "请输入6-20位密码",
		passwordAgain: "再次输入密码",
	},
	listDetail:{
		follow: "点击关注",
		newsErr: "出错了，新闻ID为空",
	},
	newsLog:{
		navigationBarTitle:"阅读记录"
	},
	bindMobile:{
		navigationBarTitle:"绑定手机号码"
	},
	api:{
		loading: "加载中...",
		uploading: "上传中...",
		submitting: "提交中...",
		saving: "保存中...",
		deleting: "删除中...",
		logging: "登录中...",
		registering: "注册中...",
		verifying: "验证中...",
		sending: "发送中...",
		processing: "处理中...",
		networkError: "网络请求失败",
		timeout: "请求超时，请检查网络连接",
		connectionFailed: "网络连接失败，请检查网络设置",
		dataFormatError: "数据格式错误",
		operationFailed: "操作失败",
		operationSuccess: "操作成功",
		loginExpired: "登录已过期，请重新登录",
		authFailed: "认证失败",
		uploadFailed: "文件上传失败",
		uploadSuccess: "上传成功",
		serverError: "服务器错误",
		permissionDenied: "权限不足",
		resourceNotFound: "资源未找到",
		badRequest: "请求参数错误"
	}
}
