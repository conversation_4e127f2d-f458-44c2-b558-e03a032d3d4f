export default {
	tabbar:'List,Grid,contacts,Mine',
	agreementsTitle:'User service agreement,Privacy policy',
	common: {
		wechatFriends: "friends",
		wechatBbs: "bbs",
		weibo: "weibo",
		more: "more",
		agree:"agree",
		copy: "copy",
		wechatApplet: "applet",
		cancelShare: "cancel sharing",
		updateSucceeded: "update succeeded",
		phonePlaceholder: "Please enter your mobile phone number",
		emailPlaceholder: "Please enter your email",
		verifyCodePlaceholder: "Please enter the verification code",
		newPasswordPlaceholder: "Please enter a new password",
		confirmNewPasswordPlaceholder: "Please confirm the new password",
		confirmPassword: "Please confirm the password",
		verifyCodeSend: "Verification code has been sent to via SMS",
		passwordDigits: "The password is 6 - 20 digits",
		getVerifyCode: "Get Code",
		noAgree: "You have not agreed to the privacy policy agreement",
		gotIt: "got it",
		login: "sign in",
		error: "error",
		complete: "complete",
		submit: "Submit",
		formatErr: "Incorrect mobile phone number format",
		sixDigitCode: "Please enter a 6-digit verification code",
		resetNavTitle:"Reset password",
        unknown: "Unknown",
        male: "Male",
        female: "Female",
        occasionally: "Occasionally (0-2 times/week)",
        regular: "Regular (3-5 times/week)",
        athlete: "Professional athlete (6+ times/week)",
        loseWeight: "Lose Weight",
        gainMuscle: "Gain Muscle",
        improveEndurance: "Improve Endurance",
        increaseStrength: "Increase Strength",
        improveFlexibility: "Improve Flexibility",
        maintainHealth: "Maintain Health",
        sportsPerformance: "Sports Performance",
        rehabilitation: "Rehabilitation",
        lackTime: "Lack of Time",
        lackMotivation: "Lack of Motivation",
        lackKnowledge: "Lack of Knowledge",
        lackEquipment: "Lack of Equipment",
        lackSpace: "Lack of Space",
        physicalLimitations: "Physical Limitations",
        workSchedule: "Work Schedule",
        familyResponsibilities: "Family Responsibilities",
        financialConstraints: "Financial Constraints",
        weatherConditions: "Weather Conditions",
        underweight: "Underweight",
        normal: "Normal",
        overweight: "Overweight",
        obese: "Obese",
	},
	list: {
		inputPlaceholder: "Please enter the search content",
	},
	search: {
		cancelText: "cancel",
		searchHistory: "search history",
		searchDiscovery: "search discovery",
		deleteAll: "delete all",
		delete: "delete",
		deleteTip: "Are you sure to clear the search history ?",
		complete: "complete",
		searchHiddenTip: "Current search found hidden",
	},
	grid: {
		grid: "Grid Assembly",
		visibleToAll: "Visible to all",
		invisibleToTourists: "Invisible to tourists",
		adminVisible: "Admin visible",
		clickTip: "Click the",
		clickTipGrid: "grid",
	},
	mine: {
		showText: "Text",
		signIn: "Check In Reward",
		signInByAd:"Check In Reward By AD",
		toEvaluate: "To Evaluate",
		readArticles: "Read Articles",
		myScore: "My Score",
		invite: "Invite Friends",
		feedback: "Problems And Feedback",
		settings: "Settings",
		about: "About",
		checkUpdate: "Check for Updates",
		clicked: "You Clicked",
		checkScore: "Please check your points after logging in",
		currentScore: "The current score is ",
		noScore: "There are currently no points",
		notLogged: "not logged in",
	},
	userinfo: {
		navigationBarTitle:"My Profile",
		ProfilePhoto: "Profile Photo",
		nickname: "Nickname",
		notSet: "not set",
		phoneNumber: "Phone Number",
		notSpecified: "Not Specified",
		setNickname: "Set Nickname ",
		setNicknamePlaceholder: "Please enter a nickname to set",
		bindPhoneNumber: "One click binding of local number",
		bindOtherLogin: "Other number binding",
		noChange: "No change",
		uploading: "uploading",
		requestFail: "Request for service failed",
		setting: "setting",
		deleteSucceeded: "Delete succeeded",
		setSucceeded: "Set successfully",
	},
	smsCode: {
		resendVerifyCode: "resend",
		phoneErrTip: "Mobile phone number format error",
		sendSuccessTip: "SMS verification code sent successfully",
	},
	loadMore: {
		noData: "No Data",
		noNetwork: "Network error",
		toSet: "Go to settings",
		error: "error",
	},
	uniFeedback: {
		navigationBarTitle: "Problems and feedback",
		msgTitle: "Message content",
		imgTitle: "Picture list",
		contacts: "contacts",
		phone: "contact number",
		submit: "submit",
	},
	settings: {
		navigationBarTitle:"Settings",
		userInfo: "Personal Data",
		personalDetails: "Personal Details",
		changePassword: "change password",
		clearTmp: "Clear Cache",
		pushServer: "push function",
		fingerPrint: "fingerprint unlock",
		facial: "face unlock",
		deactivate: "Deactivate",
		deleteAccount: "Delete Account",
		logOut: "Logout",
		login: "Login",
		changeLanguage: "Language",
		please: "please",
		successText: "success",
		failTip: "Authentication failed. Please try again",
		authFailed: "authentication failed",
		deviceNoOpen: "The device is not turned on",
		fail: "fail",
		tips: "tips",
		exitLogin: "Do you want to log out？",
		deleteAccountConfirm: "Delete Account Confirmation",
		deleteAccountWarning: "Are you sure you want to delete your account? This action will permanently delete your account and all related data, and cannot be undone!",
		deleteAccountPasswordTip: "Please enter your password to confirm account deletion:",
		passwordPlaceholder: "Please enter current password",
		cancelText: "cancel",
		confirmText: "confirm",
		clearing: "clearing",
		clearedSuccessed: "Cleared successfully",
		deleting: "Deleting...",
		deleteSuccess: "Account deleted successfully",
		deleteFailed: "Account deletion failed",
		finalDeleteConfirm: "Are you sure you want to delete your account? This action cannot be undone!",
		// Personal details page
		personalDetailsTitle: "Personal Details",
		basicInfo: "Basic Information",
		bodyInfo: "Physical Information",
		fitnessInfo: "Fitness Information",
		name: "Name",
		namePlaceholder: "Please enter your name",
		email: "Email",
		emailPlaceholder: "Please enter your email",
		gender: "Gender",
		genderPlaceholder: "Please select gender",
		birthday: "Birthday",
		birthdayPlaceholder: "Please select birthday",
		height: "Height (cm)",
		heightPlaceholder: "Please enter height",
		weight: "Weight (kg)",
		weightPlaceholder: "Please enter weight",
		bmi: "BMI",
		exerciseFrequency: "Exercise Frequency",
		exerciseFrequencyPlaceholder: "Please select exercise frequency",
		fitnessGoals: "Fitness Goals",
		obstacles: "Obstacles",
		updateProfile: "Update Profile",
		updateSuccess: "Update successful",
		updateFailed: "Update failed",
		loading: "Loading...",
		updating: "Updating...",
		loadFailed: "Load failed",
	},
	deactivate: {
		cancelText: "cancel",
		nextStep: "next step",
		navigationBarTitle:"Logout prompt"
	},
	about: {
		sacnQR: "Scan the QR Code and your friends can also download it",
		client: "applCantion",
		and: "And",
		about: "About",
	},
	invite: {
		download: "Download",
	},
	login: {
		phoneLogin: "After logging in, you can show yourself",
		phoneLoginTip: "Unregistered mobile phone numbers will be automatically registered after verification",
		getVerifyCode: "Get Code",
	},
	uniQuickLogin: {
		accountLogin: "Account",
		SMSLogin: "SMS",
		wechatLogin: "wechat",
		appleLogin: "Apple",
		oneClickLogin: "One click login",
		QQLogin: "QQ",
		xiaomiLogin: "Xiaomi",
		getProviderFail: "Failed to get service provider",
		loginErr: "Login service initialization error",
		chooseOtherLogin: "Click the third-party login",
	},
	pwdLogin: {
		pwdLogin: "User name password login",
		placeholder: "Please enter mobile number / user name",
		passwordPlaceholder: "Please input a password",
		verifyCodePlaceholder: "Please enter the verification code",
		login: "sign in",
		forgetPassword: "Forget password",
		register: "Registered account",
	},
	register: {
		navigationBarTitle:"register",
		usernamePlaceholder: "Please enter user name",
		nicknamePlaceholder: "Please enter user nickname",
		passwordDigitsPlaceholder: "Please enter a 6-20 digit password",
		passwordAgain: "Enter the password again",
		registerAndLogin: "Register and log in",
	},
	listDetail: {
		follow: "Click follow",
		newsErr: "Error, news ID is empty",
	},
	newsLog:{
		navigationBarTitle:"Reading Log"
	},
	bindMobile:{
		navigationBarTitle:"Bind Mobile"
	},
	api:{
		loading: "Loading...",
		uploading: "Uploading...",
		submitting: "Submitting...",
		saving: "Saving...",
		deleting: "Deleting...",
		logging: "Logging in...",
		registering: "Registering...",
		verifying: "Verifying...",
		sending: "Sending...",
		processing: "Processing...",
		networkError: "Network request failed",
		timeout: "Request timeout, please check network connection",
		connectionFailed: "Network connection failed, please check network settings",
		dataFormatError: "Data format error",
		operationFailed: "Operation failed",
		operationSuccess: "Operation successful",
		loginExpired: "Login expired, please login again",
		authFailed: "Authentication failed",
		uploadFailed: "File upload failed",
		uploadSuccess: "Upload successful",
		serverError: "Server error",
		permissionDenied: "Permission denied",
		resourceNotFound: "Resource not found",
		badRequest: "Invalid request parameters"
	}
}
