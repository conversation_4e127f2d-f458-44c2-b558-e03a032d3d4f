<?xml version="1.0" encoding="UTF-8"?>
<module type="RUBY_MODULE" version="4">
  <component name="FacetManager">
    <facet type="RailsFacetType" name="Ruby on Rails">
      <configuration>
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_SUPPORT_REMOVED" VALUE="false" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_TESTS_SOURCES_PATCHED" VALUE="true" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_APPLICATION_ROOT" VALUE="$MODULE_DIR$" />
      </configuration>
    </facet>
  </component>
  <component name="ModuleRunConfigurationManager">
    <shared />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/features" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.bundle" />
      <excludeFolder url="file://$MODULE_DIR$/components" />
      <excludeFolder url="file://$MODULE_DIR$/log" />
      <excludeFolder url="file://$MODULE_DIR$/public/packs" />
      <excludeFolder url="file://$MODULE_DIR$/public/system" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cache" />
      <excludeFolder url="file://$MODULE_DIR$/uni-app/unpackage/dist/dev/cache" />
      <excludeFolder url="file://$MODULE_DIR$/uni-starter/unpackage/cache" />
      <excludeFolder url="file://$MODULE_DIR$/uni-starter" />
      <excludeFolder url="file://$MODULE_DIR$/vue-vben-admin" />
    </content>
    <orderEntry type="jdk" jdkName="mise: 3.4.5" jdkType="RUBY_SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="PROVIDED" name="actioncable (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailbox (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailer (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionpack (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actiontext (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionview (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activejob (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activemodel (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activerecord (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activestorage (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activesupport (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="addressable (v2.8.7, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="annotaterb (v4.19.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ast (v2.4.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-eventstream (v1.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-partitions (v1.1155.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-core (v3.232.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-kms (v1.112.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-s3 (v1.198.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sigv4 (v1.12.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="base64 (v0.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bcrypt (v3.1.20, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bcrypt_pbkdf (v1.1.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="benchmark (v0.4.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bigdecimal (v3.2.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bootsnap (v1.18.6, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="brakeman (v7.1.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="builder (v3.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bundler (v2.6.9, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="concurrent-ruby (v1.3.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="config (v5.6.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="connection_pool (v2.5.4, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="crass (v1.0.6, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="cronex (v0.15.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="csv (v3.3.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="date (v3.4.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="debug (v1.11.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="deep_merge (v1.2.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="diff-lcs (v1.6.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="discard (v1.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv (v3.1.8, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="drb (v2.2.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ed25519 (v1.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="erb (v5.0.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="erubi (v1.13.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="et-orbi (v1.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="event_stream_parser (v1.0.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday (v2.13.4, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-multipart (v1.1.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-net_http (v3.4.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi (v1.17.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="fugit (v1.11.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="globalid (v1.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hashie (v5.0.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="httparty (v0.23.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="i18n (v1.14.7, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="image_processing (v1.14.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="io-console (v0.8.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="irb (v1.15.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jmespath (v1.6.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json (v2.13.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json-schema (v5.2.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jwt (v3.1.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="kamal (v2.7.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="language_server-protocol (v3.17.0.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="lint_roller (v1.1.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="logger (v1.7.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="loofah (v2.24.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mail (v2.8.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="marcel (v1.0.4, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_magick (v5.3.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_mime (v1.1.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="minitest (v5.25.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="msgpack (v1.8.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multi_xml (v0.7.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multipart-post (v2.4.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-http (v0.6.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-imap (v0.5.10, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-pop (v0.1.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-protocol (v0.2.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-scp (v4.1.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-sftp (v4.0.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-smtp (v0.5.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-ssh (v7.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nio4r (v2.7.4, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nokogiri (v1.18.9, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ostruct (v0.6.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pagy (v9.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="parallel (v1.27.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="parser (v3.3.9.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pg (v1.6.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pp (v0.6.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="prettyprint (v0.2.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="prism (v1.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="psych (v5.2.6, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="public_suffix (v6.0.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="puma (v7.0.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="raabro (v1.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="racc (v1.8.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack (v3.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-cors (v3.0.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-session (v2.1.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-test (v2.2.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rackup (v2.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-dom-testing (v2.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-html-sanitizer (v1.6.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-i18n (v8.0.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="railties (v8.0.2.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rainbow (v3.1.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rake (v13.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rdoc (v6.14.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis (v5.4.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-client (v0.25.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="regexp_parser (v2.11.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="reline (v0.6.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-core (v3.13.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-expectations (v3.13.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-mocks (v3.13.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails (v8.0.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-support (v3.13.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rswag-api (v2.16.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rswag-specs (v2.16.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rswag-ui (v2.16.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop (v1.80.2, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-ast (v1.46.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-performance (v1.26.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-rails (v2.33.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-rails-omakase (v1.1.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-openai (v8.3.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-progressbar (v1.13.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-vips (v2.2.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="securerandom (v0.4.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sequel (v5.96.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sidekiq (v8.0.7, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sidekiq-cron (v2.3.1, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sqlite3 (v2.7.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sshkit (v1.24.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stringio (v3.1.7, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="thor (v1.4.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="thruster (v0.1.15, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="timeout (v0.4.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tzinfo (v2.0.6, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode (v0.4.4.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode-display_width (v3.1.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode-emoji (v4.0.4, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uri (v1.0.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="useragent (v0.16.11, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-driver (v0.8.0, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-extensions (v0.1.5, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zeitwerk (v2.7.3, mise: 3.4.5) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zstd-ruby (v1.5.7.0, mise: 3.4.5) [gem]" level="application" />
  </component>
  <component name="RModuleSettingsStorage">
    <LOAD_PATH number="0" />
    <I18N_FOLDERS number="1" string0="$MODULE_DIR$/config/locales" />
  </component>
  <component name="RailsPaths" isManagedAutomatically="true">
    <entry key="app">
      <value>file://$MODULE_DIR$/app</value>
    </entry>
    <entry key="app/assets">
      <value>file://$MODULE_DIR$/app/assets</value>
    </entry>
    <entry key="app/channels">
      <value>file://$MODULE_DIR$/app/channels</value>
    </entry>
    <entry key="app/controllers">
      <value>file://$MODULE_DIR$/app/controllers</value>
    </entry>
    <entry key="app/helpers">
      <value>file://$MODULE_DIR$/app/helpers</value>
    </entry>
    <entry key="app/mailers">
      <value>file://$MODULE_DIR$/app/mailers</value>
    </entry>
    <entry key="app/models">
      <value>file://$MODULE_DIR$/app/models</value>
    </entry>
    <entry key="app/views">
      <value>file://$MODULE_DIR$/app/views</value>
    </entry>
    <entry key="config">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="config/cable">
      <value>file://$MODULE_DIR$/config/cable.yml</value>
    </entry>
    <entry key="config/cache">
      <value>file://$MODULE_DIR$/config/cache.yml</value>
    </entry>
    <entry key="config/database">
      <value>file://$MODULE_DIR$/config/database.yml</value>
    </entry>
    <entry key="config/environment">
      <value>file://$MODULE_DIR$/config/environment.rb</value>
    </entry>
    <entry key="config/environments">
      <value>file://$MODULE_DIR$/config/environments</value>
    </entry>
    <entry key="config/initializers">
      <value>file://$MODULE_DIR$/config/initializers</value>
    </entry>
    <entry key="config/locales">
      <value>file://$MODULE_DIR$/config/locales</value>
    </entry>
    <entry key="config/routes">
      <value>file://$MODULE_DIR$/config/routes</value>
    </entry>
    <entry key="config/routes.rb">
      <value>file://$MODULE_DIR$/config/routes.rb</value>
    </entry>
    <entry key="config/solid_cache">
      <value>file://$MODULE_DIR$/config/solid_cache.yml</value>
    </entry>
    <entry key="db">
      <value>file://$MODULE_DIR$/db</value>
    </entry>
    <entry key="db/migrate">
      <value>file://$MODULE_DIR$/db/migrate</value>
    </entry>
    <entry key="db/seeds.rb">
      <value>file://$MODULE_DIR$/db/seeds.rb</value>
    </entry>
    <entry key="lib">
      <value>file://$MODULE_DIR$/lib</value>
    </entry>
    <entry key="lib/assets">
      <value>file://$MODULE_DIR$/lib/assets</value>
    </entry>
    <entry key="lib/tasks">
      <value>file://$MODULE_DIR$/lib/tasks</value>
    </entry>
    <entry key="lib/templates">
      <value>file://$MODULE_DIR$/lib/templates</value>
    </entry>
    <entry key="log">
      <value>file://$MODULE_DIR$/log/development.log</value>
    </entry>
    <entry key="public">
      <value>file://$MODULE_DIR$/public</value>
    </entry>
    <entry key="public/javascripts">
      <value>file://$MODULE_DIR$/public/javascripts</value>
    </entry>
    <entry key="public/stylesheets">
      <value>file://$MODULE_DIR$/public/stylesheets</value>
    </entry>
    <entry key="test/mailers/previews">
      <value>file://$MODULE_DIR$/test/mailers/previews</value>
      <value>file://$MODULE_DIR$/test/mailers/previews</value>
    </entry>
    <entry key="tmp">
      <value>file://$MODULE_DIR$/tmp</value>
    </entry>
    <entry key="vendor">
      <value>file://$MODULE_DIR$/vendor</value>
    </entry>
    <entry key="vendor/assets">
      <value>file://$MODULE_DIR$/vendor/assets</value>
    </entry>
  </component>
</module>